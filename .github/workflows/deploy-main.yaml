name: Deploy LLM Microservice to Cloud Run (MAIN)

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Cache Pip Dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Google Cloud Auth
        uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: '${{ secrets.GCP_GOODFOLIO_MAIN_SA_KEY }}'
          project_id: goodfolio-main

      - name: Set up Cloud SDK
        uses: 'google-github-actions/setup-gcloud@v2'

      - name: Set up Docker Authentication
        run: |
          gcloud auth configure-docker --quiet

      - name: Install Python & Dependencies
        run: |
          sudo apt-get update && sudo apt-get install -y python3-pip
          python3 -m pip install --upgrade pip
          pip install --cache-dir ~/.cache/pip google-cloud-bigquery python-dotenv

      - name: Initialize BigQuery Tables
        env:
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GCP_GOODFOLIO_MAIN_SA_KEY }}
          RUN_MODE: ${{ github.ref_name }}
        run: |
          echo "$GOOGLE_APPLICATION_CREDENTIALS" > gcp-key.json
          export GOOGLE_APPLICATION_CREDENTIALS=gcp-key.json
          export PYTHONPATH=$PYTHONPATH:$(pwd)
          # python utils/gcp_bigquery_init.py

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker Layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build and Push Docker Image
        run: |
          IMAGE="gcr.io/goodfolio-main/llm-microservice:latest"
          docker buildx build \
            --cache-to=type=local,dest=/tmp/.buildx-cache \
            --cache-from=type=local,src=/tmp/.buildx-cache \
            -t "$IMAGE" . \
            --push

      - name: Deploy to Cloud Run
        run: |
          gcloud run deploy llm-microservice \
            --image "gcr.io/goodfolio-main/llm-microservice:latest" \
            --add-cloudsql-instances "goodfolio-main:europe-west2:goodfolio-llm-app-maindbs" \
            --platform managed \
            --region europe-west2 \
            --allow-unauthenticated \
            --set-env-vars RUN_MODE=main,INSTANCE_CONNECTION_NAME=goodfolio-main:europe-west2:goodfolio-llm-app-maindbs,ENV=main \
            --memory 1Gi \
            --cpu 1 \
            --timeout 600 \
            --min-instances 1 \
            --concurrency 20 \
            --service-account "<EMAIL>"

  # post-deployment-test:
  #   runs-on: ubuntu-latest
  #   needs: deploy
  #   if: ${{ needs.deploy.result == 'success' }}
  #   steps:
  #     - name: Checkout code
  #       uses: actions/checkout@v3

  #     - name: Install dependencies
  #       run: |
  #         pip install --upgrade pip
  #         pip install -r tests/api/requirements.txt

  #     - name: Run API Tests
  #       run: |
  #         pytest tests/api --maxfail=5 --disable-warnings -v
  #       env:
  #         TEST_INFORMATION_DEV: ${{ secrets.TEST_INFORMATION_DEV }}