exclude: '^(\\.git/|.*cache.*/|.venv/|.*\\.egg-info/|.*\\.tox/|.*\\.mypy_cache/|.*\\.pytest_cache/|.*\\.vscode/|.*\\.vs/|.*\\.idea|venv)'

repos:

  # - repo: https://github.com/pre-commit/mirrors-mypy
  #   rev: v1.8.0
  #   hooks:
  #     - id: mypy
  #       args: [
  #           --check-untyped-defs,
  #           --ignore-missing-imports,
  #           # --disallow-untyped-defs,
  #           # "--disallow-incomplete-defs",
  #         ]
  #       additional_dependencies: [types-redis, types-pytz, types-requests]

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.9.10
    hooks:
      - id: ruff
        args: [--fix]
        exclude: \.ipynb$
      - id: ruff-format
        exclude: \.ipynb$

  # - repo: local
  #   hooks:
  #     - id: pytest
  #       name: Run pytest
  #       entry: pytest
  #       language: system
  #       types: [python]
  #       pass_filenames: false
  #       args: ["--maxfail=1", "--disable-warnings"]
