# finspector-ml-api
[![image.png](https://i.postimg.cc/d3fWmJYr/image.png)](https://postimg.cc/64dLBJTp)

Welcome to the Finspector ML API repository: a microservice that handles all AI and ML functionalities.

## Local Development Setup

For local development, we recommend using Docker Compose for dependencies (like PostgreSQL) while running the FastAPI code directly on your host machine for quick development and testing.

### Step 1: Configure Environment Variables

For local development, we use a `.env` file in the `app` folder. You’ll need to populate this file with the required variables to run the project locally. You’ll find detailed instructions within the file.

Copy `./app/.env.example` to `./app/.env` and configure the values.

### Step 2: Configure Google Cloud Credentials

First, request access to the `goodfolio-dev` project from someone with access, typically our CTO. Then install the gcloud CLI on your host machine.

Set the correct project:

```bash
gcloud config set project goodfolio-dev
```

Log in and generate credentials:

```bash
gcloud auth application-default login
```

Create a secrets directory and copy credentials:

```bash
mkdir -p secrets
cp ~/.config/gcloud/application_default_credentials.json secrets/
```

### Step 3: Set Up Dependencies (PostgreSQL and pgAdmin) Using Docker

We use `postgres:14.17-bookworm` in the docker-compose file to match our cloud SQL database version. This ensures consistency between local development and production environments. Update the docker compose file if the production version changes.

```bash
docker compose -f docker-compose-local.dependencies.yml up
```

Make sure the `POSTGRES_USER`, `POSTGRES_PASSWORD`, and `POSTGRES_DB` variables match your `DATABASE_URL` in the `.env` file. These values are for local development only.

### Step 4: Run the FastAPI Application

As of now, this project requires Python 3.12 to maintain consistency with production. Use the same Python version specified in the Dockerfile.

Create and activate a virtual environment:

```bash
python3.12 -m venv .venv
source .venv/bin/activate
```

Next, upgrade pip and install the requirements:

```bash
pip install --upgrade pip
pip install -r requirements.txt

```

Finally, start the FastAPI application:

```bash
python -m app.main

```

The application will be available at [http://localhost:8000](http://localhost:8000/)

### Step 5: Populate the Database

Currently, the `finspector-ml-api` microservice shares a database with the Django-based `goodfolio-llm-app` project. You'll need to populate the `prompts` and `language_models` tables to run this project. To populate these tables:

1. Access GCP Secret Manager and locate the `goodfolio-llm-app` secrets
2. Download the latest 'Enabled' version to find the `dbname`, `dbuser`, and `dbpass` values
3. In the GCP dashboard, go to SQL instances and select the `goodfolio-llm-app-devdbs` database
4. Open Cloud SQL Studio and query the `prompts` and `language_models` tables
5. Copy the values from these tables to your local database

The project requires this data to function properly.

### Database Migrations

To manage database schema changes, use these commands:

1. **Generate a Migration File:** Run this script, replacing `<migration-name>` with a descriptive name:
    
    ```bash
    ./generate_migration.sh <migration-name>;
    ```
    
2. **Apply Migrations:** Run this command to update the database schema:
    
    ```bash
    ./run_migrations.sh
    ```
    

Before running these scripts, ensure all dependencies are installed and configured.

## File Naming Convention

All file names must be lowercase and use underscores (`_`) as word separators. Include the contextual category in the filename. For example, files in the **routes** category should be named like: user_**route**.py
