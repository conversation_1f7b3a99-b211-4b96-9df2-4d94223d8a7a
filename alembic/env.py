import os
from logging.config import fileConfig

import sqlmodel
from dotenv import load_dotenv
from sqlalchemy import engine_from_config, pool

from alembic import context

load_dotenv(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "app", ".env")))

config = context.config
config.set_main_option("sqlalchemy.url", os.getenv("DATABASE_URL"))
if config.config_file_name is not None:
    fileConfig(config.config_file_name)


target_metadata = sqlmodel.SQLModel.metadata


def include_object(object, name, type_, reflected, compare_to):
    """
    Filter function for Alembic autogenerate.

    Will only include tables that are part of this application's SQLModel metadata.
    This prevents <PERSON>embic from trying to drop tables from other applications
    (like a Django service) that share the same database.
    """
    if type_ == "table" and object.metadata is not target_metadata:
        return False
    return True


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        include_object=include_object,
        render_as_batch=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            include_object=include_object,
            render_as_batch=True,
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
