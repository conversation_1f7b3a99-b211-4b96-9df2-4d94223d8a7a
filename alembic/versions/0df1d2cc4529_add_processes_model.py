"""Add processes model

Revision ID: 0df1d2cc4529
Revises: d9436c9583d5
Create Date: 2025-01-15 02:32:24.531722

"""

from typing import Sequence, Union
import sqlmodel

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "0df1d2cc4529"
down_revision: Union[str, None] = "d9436c9583d5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "processes",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("no", sa.Integer(), autoincrement=True, nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("model_id", sa.Uuid(), nullable=False),
        sa.Column("prompt_id", sa.Uuid(), nullable=False),
        sa.Column("chunk_text", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("result_id", sa.Uuid(), nullable=True),
        sa.Column("result_type", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("input_tokens", sa.Integer(), nullable=False),
        sa.Column("output_tokens", sa.Integer(), nullable=True),
        sa.Column("request_id", sa.Uuid(), nullable=False),
        sa.ForeignKeyConstraint(
            ["model_id"],
            ["language_models.id"],
        ),
        sa.ForeignKeyConstraint(
            ["prompt_id"],
            ["prompts.id"],
        ),
        sa.ForeignKeyConstraint(
            ["request_id"],
            ["requests.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_processes_request_id"), "processes", ["request_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_processes_request_id"), table_name="processes")
    op.drop_table("processes")
    # ### end Alembic commands ###
