"""add outbox table

Revision ID: 4159d88c2e95
Revises: f59797630d8b
Create Date: 2025-07-23 10:54:55.963243

"""
from typing import Sequence, Union
import sqlmodel

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4159d88c2e95'
down_revision: Union[str, None] = 'f59797630d8b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('outbox',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('correlation_id', sa.Uuid(), nullable=False),
    sa.Column('processing_attempts', sa.Integer(), nullable=False),
    sa.Column('error_message', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('payload', sa.JSON(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', name='outboxstatus'), nullable=False),
    sa.Column('available_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), onupdate=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['correlation_id'], ['requests.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('outbox', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_outbox_available_at'), ['available_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_outbox_correlation_id'), ['correlation_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_outbox_status'), ['status'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('outbox', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_outbox_status'))
        batch_op.drop_index(batch_op.f('ix_outbox_correlation_id'))
        batch_op.drop_index(batch_op.f('ix_outbox_available_at'))

    op.drop_table('outbox')
    # ### end Alembic commands ###
