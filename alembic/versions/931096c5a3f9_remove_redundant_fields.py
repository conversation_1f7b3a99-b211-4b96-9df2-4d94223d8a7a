"""remove_redundant_fields

Revision ID: 931096c5a3f9
Revises: e5b19707eac3
Create Date: 2024-12-22 19:31:45.245741

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "931096c5a3f9"
down_revision: Union[str, None] = "e5b19707eac3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "phone_number")
    op.drop_column("users", "name")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users",
        sa.Column("name", sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    )
    op.add_column(
        "users",
        sa.Column("phone_number", sa.VARCHAR(length=20), autoincrement=False, nullable=True),
    )
    # ### end Alembic commands ###
