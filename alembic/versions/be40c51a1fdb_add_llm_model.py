"""Add LLM model

Revision ID: be40c51a1fdb
Revises: 931096c5a3f9
Create Date: 2025-01-10 11:59:24.774163

"""

from typing import Sequence, Union
import sqlmodel

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "be40c51a1fdb"
down_revision: Union[str, None] = "931096c5a3f9"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "language_models",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("no", sa.Integer(), autoincrement=True, nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("model_name", sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
        sa.Column("input_token_cost", sa.Float(), nullable=False),
        sa.Column("output_token_cost", sa.Float(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_language_models_model_name"),
        "language_models",
        ["model_name"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_language_models_model_name"), table_name="language_models")
    op.drop_table("language_models")
    # ### end Alembic commands ###
