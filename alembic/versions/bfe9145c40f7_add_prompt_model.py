"""Add Prompt model

Revision ID: bfe9145c40f7
Revises: be40c51a1fdb
Create Date: 2025-01-10 12:00:31.204995

"""

from typing import Sequence, Union
import sqlmodel

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "bfe9145c40f7"
down_revision: Union[str, None] = "be40c51a1fdb"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "prompts",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("no", sa.Integer(), autoincrement=True, nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("version", sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
        sa.<PERSON>umn("assistant_role", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("instrucion", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("fewshot_instruction", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("response_structure", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_prompts_version"), "prompts", ["version"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_prompts_version"), table_name="prompts")
    op.drop_table("prompts")
    # ### end Alembic commands ###
