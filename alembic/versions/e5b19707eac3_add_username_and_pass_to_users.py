"""add username and pass to users

Revision ID: e5b19707eac3
Revises: 557b338ae44d
Create Date: 2024-10-31 01:01:25.804681

"""

from typing import Sequence, Union
import sqlmodel

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e5b19707eac3"
down_revision: Union[str, None] = "557b338ae44d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users",
        sa.Column("username", sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    )
    op.add_column(
        "users",
        sa.Column("password", sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    )
    op.create_unique_constraint(None, "users", ["username"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "users", type_="unique")
    op.drop_column("users", "password")
    op.drop_column("users", "username")
    # ### end Alembic commands ###
