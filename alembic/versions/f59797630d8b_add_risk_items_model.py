"""Add risk_items model

Revision ID: f59797630d8b
Revises: 0df1d2cc4529
Create Date: 2025-01-15 02:33:33.246511

"""

from typing import Sequence, Union
import sqlmodel

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "f59797630d8b"
down_revision: Union[str, None] = "0df1d2cc4529"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "risk_items",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("no", sa.Integer(), autoincrement=True, nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("process_id", sa.Uuid(), nullable=False),
        sa.Column("chunk_id", sa.Integer(), nullable=False),
        sa.Column("violated_rule_id", sa.Integer(), nullable=False),
        sa.Column("violation_text", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("violation_explanation", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("verified", sa.Boolean(), nullable=True),
        sa.Column("expert_opinion", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.ForeignKeyConstraint(
            ["process_id"],
            ["processes.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_risk_items_process_id"), "risk_items", ["process_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_risk_items_process_id"), table_name="risk_items")
    op.drop_table("risk_items")
    # ### end Alembic commands ###
