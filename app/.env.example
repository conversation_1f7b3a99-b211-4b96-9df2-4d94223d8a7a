# The .env file is used only for local development. 
# For development, test, or production (main), environment variables are set using Google Cloud Secret Manager.
# ENV variable must be one of: LOCAL_DEVELOPMENT, DEVELOPMENT, TEST, MAIN

ENV=LOCAL_DEVELOPMENT

# Database connection string for PostgreSQL using SQLAlchemy
# Format: postgresql+psycopg2://[username]:[password]@[host]:[port]/[database]
# - Uses psycopg2-binary (psycopg 2). For psycopg 3, use 'postgresql+psycopg'
# - Default credentials: username='userpostgres', password='c4HgtsgtZNXgzdPr'
# - You may modify credentials, but remember to update docker compose variables
# - In production, credentials are retrieved from GCP Secret Manager
# - Default host: 127.0.0.1:5432
# - Database name: mlapi

DATABASE_URL=postgresql+psycopg2://userpostgres:c4HgtsgtZNXgzdPr@127.0.0.1:5432/mlapi

# Controls whether database migrations run on project startup
RUN_MIGRATIONS=false

# JWT configuration - customize the secret key as needed
JWT_SECRET_KEY=JHFYTF765HFGHJGF76
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=3600

# Google Cloud PubSub configuration
# We use three projects: goodfolio-dev, goodfolio-test, and goodfolio-main
# For local development, set this to goodfolio-dev
GOOGLE_PROJECT_ID=goodfolio-dev

# PubSub GCP service settings for inter-project communication
PUBSUB_PROMPT_TOPIC=llm-prompt
PUBSUB_RESPONSE_TOPIC=llm-response
PUBSUB_PROMPT_SUBSCRIPTION=llm-prompt-sub
PUBSUB_RESPONSE_SUBSCRIPTION=llm-response-sub

# To set the following variables:
# 1. Ensure you have access to the goodfolio-dev GCP project
# 2. Go to Secret Manager
# 3. Navigate to finspector-llm-microservice-dev
# 4. Download the latest 'Enabled' version
# 5. Copy the values to these environment variables

SLACK_BOT_TOKEN=
SLACK_CHANNEL=
OPENAI_API_KEY=