from enum import Enum
import json
import traceback

from app.enums.error_enum import <PERSON>rror<PERSON><PERSON>
from app.dependencies.logger_dependency import logger
from app.utils.exclude_keys_from_dict_util import exclude_keys_from_dict


class CustomException(Exception):
    def __init__(
        self,
        message: str,
        error_code: ErrorCodes = ErrorCodes.UNKNOWN_ERROR,
        detail: dict = {},
        original_exception: Exception | None = None,
    ):
        super().__init__(message)
        self.original_exception = original_exception
        self.error_code = error_code
        self.detail = detail

    def __str__(self):
        """
        Customizes exception string format.
        """
        if self.error_code:
            return f"[{self.error_code}]: {self.args[0]}"
        else:
            return self.args[0]

    def log(self):
        """
        Logs the exception using json logger.
        """
        error_log = self.__dict__
        if isinstance(error_log["error_code"], Enum):
            error_log["error_code"] = error_log["error_code"].value
        error_log["stack"] = traceback.format_exception(self.original_exception)
        error_log = exclude_keys_from_dict(error_log, ["original_exception"])

        try:
            error_log = json.dumps(error_log)
        except Exception:
            pass

        logger.error(error_log)
