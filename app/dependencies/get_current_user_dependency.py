import jwt
from typing import Annotated
from fastapi import Depends, HTTPException, Request, status
from fastapi.security import <PERSON>Auth<PERSON><PERSON><PERSON><PERSON><PERSON>earer
from sqlmodel import Session


from app.dtos.user_dto import UserDto
from app.enums.error_enum import ErrorCodes
from app.utils.jwt_util import ALG<PERSON><PERSON><PERSON><PERSON>, JWT_SECRET_KEY
import app.services.user_service as user_service


oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/user/token")


def get_current_user(token: Annotated[str, Depends(oauth2_scheme)], request: Request) -> UserDto:
    """
    Get user data on request dependency.

    Parameters:
    user_id (str): user id used for finding user.
    db (Session): database session.

    Returns:
    User with the menus they can access.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail={
            "message": "Could not validate credentials",
            "error_code": ErrorCodes.INVALID_CREDENTIALS,
        },
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("user_id")
        if user_id is None:
            raise credentials_exception
    except Exception:
        raise credentials_exception

    db: Session = request.state.db
    user = user_service.get_user(user_id, db)
    db.expunge_all()
    return user
