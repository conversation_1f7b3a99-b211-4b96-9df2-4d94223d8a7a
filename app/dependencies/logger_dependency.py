import logging


class CustomLogger:
    """
    Custom logger class that integrates with Sentry for error tracking.
    It provides methods for logging messages at different levels (debug, info, warning, error).
    It also formats the log messages with colors for better readability in the console.
    """

    COLORS = {
        "INFO": "\033[92m",  # Green
        "ERROR": "\033[91m",  # Red
        "DEBUG": "\033[94m",  # Blue
        "WARNING": "\033[93m",  # Yellow
        "RESET": "\033[0m",
    }

    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s: %(message)s",
            handlers=[logging.StreamHandler()],
        )

    def _format_message(self, level: str, message: str) -> str:
        color = self.COLORS.get(level, self.COLORS["RESET"])
        return "%s%s%s" % (color, message, self.COLORS["RESET"])

    def debug(self, message):
        colored_message = self._format_message("DEBUG", message)
        self.logger.debug(colored_message)

    def info(self, message):
        colored_message = self._format_message("INFO", message)
        self.logger.info(colored_message)

    def warning(self, message):
        colored_message = self._format_message("WARNING", message)
        self.logger.warning(colored_message)

    def error(self, message, exc: Exception | None = None):
        colored_message = self._format_message("ERROR", message)
        self.logger.error(colored_message)
        if exc:
            self.logger.exception(exc)


logger = CustomLogger(__name__)
