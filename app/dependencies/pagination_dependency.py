from fastapi import Request


class Pagination:
    def __init__(self, page: int = 1, limit: int = 10):
        self.page = page
        self.limit = limit

    @property
    def offset(self) -> int:
        return (self.page - 1) * self.limit


def get_pagination(request: Request) -> Pagination:
    """
    Builds Pagination class based on input query
    """
    page = int(request.query_params.get("page", 1))
    limit = int(request.query_params.get("limit", 10))
    return Pagination(page=page, limit=limit)
