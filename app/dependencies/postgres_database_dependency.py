import os
from typing import Annotated
from fastapi import Depends, Request
from sqlmodel import SQLModel, Session, create_engine
from alembic.config import Config
from alembic import command

from app.dependencies.logger_dependency import logger


DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+psycopg2://user:password@localhost/dbname")
engine = create_engine(
    DATABASE_URL,
    pool_size=10,
    max_overflow=20,
    pool_recycle=3600,
)


def get_session():
    """
    Return a database session.
    """
    with Session(engine, expire_on_commit=False) as session:
        yield session


def get_session_from_req(request: Request):
    """
    Used as request dependency to get db from request.

    There is a global middleware registerd in the main file that
    gets and stores the db session in the request.
    """
    return request.state.db


SessionDep = Annotated[Session, Depends(get_session)]


def run_migrations():
    """
    Runs db migrations.
    """
    if os.getenv("RUN_MIGRATIONS", "false").lower() == "true":
        logger.info("Running Migrations")
        alembic_cfg = Config("alembic.ini")
        alembic_cfg.set_main_option("sqlalchemy.url", DATABASE_URL)
        command.upgrade(alembic_cfg, "head")
    else:
        logger.info("Ignored Migrations")


def create_db_and_tables():
    SQLModel.metadata.create_all(engine)
