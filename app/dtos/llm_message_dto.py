import uuid

from pydantic import BaseModel

from app.dtos.risk_item_dto import RiskItemDto
from app.enums.llm_enum import LlmModel, PromptType


class LlmPromptMessageDto(BaseModel):
    request_id: uuid.UUID
    input_text: list[str]
    rule_set: list[dict]
    model_name: LlmModel
    prompt_type: PromptType


class LlmResponseMessageDto(BaseModel):
    risk_item_dto: RiskItemDto
    input_token_count: int
    output_token_count: int
    submission_cost: float
