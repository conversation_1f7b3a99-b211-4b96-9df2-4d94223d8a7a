from pydantic import BaseModel, model_validator


class SingleRiskItemDto(BaseModel):
    rule_id: int | None = None
    violation_text: str | None = None
    violation_explanation: str | None = ""

    @model_validator(mode="before")
    @classmethod
    def ensure_proper_format(cls, value):
        if not isinstance(value, dict):
            return

        value = {k: v for k, v in value.items() if k in cls.model_fields.keys()}

        if not value:
            return {}

        if "rule_id" in value:
            try:
                value["rule_id"] = int(value["rule_id"])
            except Exception:
                value["rule_id"] = None

        return value


class RiskItemDto(BaseModel):
    risk_items: list[SingleRiskItemDto] = list()

    @model_validator(mode="before")
    @classmethod
    def ensure_proper_format(cls, values):
        if isinstance(values, cls):
            return values
        risk_items_input = values.get("risk_items", [])
        if isinstance(risk_items_input, dict):
            risk_items_input = [risk_items_input]

        processed_items = []
        for item in risk_items_input:
            if isinstance(item, SingleRiskItemDto):
                processed_items.append(item)
                continue
            if isinstance(item, dict):
                if item and item.get("rule_id") and item.get("violation_text"):
                    processed_items.append(item)

        return {"risk_items": processed_items}
