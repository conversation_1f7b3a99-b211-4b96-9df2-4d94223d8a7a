from enum import StrEnum


class LlmModel(StrEnum):
    GEMINI_FLASH_20 = "gemini-2.0-flash"
    GEMINI_PRO_15 = "gemini-1.5-pro"
    GEMINI_PRO_25 = "gemini-2.5-pro"
    GEMINI_FLASH_25 = "gemini-2.5-flash-preview-05-20"


class LlmPromptStatus(StrEnum):
    CREATED = "created"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class PromptType(StrEnum):
    FILE = "file"
    TICKET = "ticket"
    SOCIAL_MEDIA = "social_media"
