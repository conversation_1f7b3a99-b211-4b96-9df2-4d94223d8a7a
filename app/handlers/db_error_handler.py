from fastapi import Request, status
from fastapi.responses import JSONResponse
from sqlalchemy.exc import IntegrityError
from sqlmodel import Session

from app.enums.error_enum import ErrorCodes
from app.dtos.error_dto import ErrorDto
from app.handlers.error_handler import error_handler


async def db_error_handler(request: Request, exc: IntegrityError):
    """
    Handles database erros in the app.

    Parameters:
    request (Request): the request that faced error.
    exc     (IntegrityError): the exception.

    Returns:
    JSON response to best sent as response.
    """
    db: Session = request.state.db
    db.rollback()

    error_code = ErrorCodes.INTERNAL_SERVER_ERROR
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_message = str(exc.orig.diag.message_detail)
    if exc.orig.pgcode == "23505":
        error_code = ErrorCodes.UNIQUE_CONSTRAINT
        status_code = status.HTTP_409_CONFLICT

    custom_error = error_handler(
        exc=exc,
        error_code=error_code,
        detail={
            "path": request.url.path,
            "x-correlation-id": request.headers.get("x-correlation-id"),
        },
        message=error_message,
        status_code=status_code,
    )
    error_response = ErrorDto(
        status_code=status_code,
        error_code=custom_error.error_code,
        message=str(custom_error),
        detail=custom_error.detail,
    )
    return JSONResponse(
        status_code=status_code,
        content=error_response.model_dump(exclude_none=True),
    )
