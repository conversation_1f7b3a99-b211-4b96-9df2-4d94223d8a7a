from app.enums.error_enum import ErrorCodes
from app.dependencies.custom_exception_dependency import CustomException
from app.utils.log_error_to_slack_util import log_error_to_slack


def error_handler(
    exc: Exception,
    error_code: ErrorCodes = ErrorCodes.UNKNOWN_ERROR,
    detail: dict = {},
    message: str | None = None,
    status_code: int = 0,
) -> CustomException:
    """
    Handles error.

    Parameters:
    exc     (Exception): the exception.

    Example:
    error_handler(exc=Exception("some error"), error_code=ErrorCodes.UNKNOWN_ERROR, detail={"has_detail": True})
    """
    if not message:
        message = str(exc)
    error_response = CustomException(message=message, error_code=error_code, detail=detail, original_exception=exc)
    error_response.log()
    if status_code >= 500:
        log_error_to_slack(message, error_response)
    return error_response
