from fastapi import Request, status
from fastapi.responses import JSONResponse

from app.enums.error_enum import ErrorCodes
from app.dtos.error_dto import ErrorDto
from app.handlers.error_handler import error_handler


async def general_error_handler(request: Request, exc: Exception):
    """
    Handles general or uncaught erros in the app.

    Parameters:
    request (Request): the request that faced error.
    exc     (Exception): the exception.

    Returns:
    JSON response to best sent as response.
    """
    custom_error = error_handler(
        exc=exc,
        error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
        detail={
            "path": request.url.path,
            "x-correlation-id": request.headers.get("x-correlation-id"),
        },
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )
    error_response = ErrorDto(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code=custom_error.error_code,
        message=str(custom_error),
        detail=custom_error.detail,
    )
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response.model_dump(exclude_none=True),
    )
