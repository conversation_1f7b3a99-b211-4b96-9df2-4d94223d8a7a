from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse

from app.enums.error_enum import ErrorCodes
from app.dtos.error_dto import ErrorDto
from app.utils.add_to_dict_util import add_to_dict
from app.utils.exclude_keys_from_dict_util import exclude_keys_from_dict
from app.handlers.error_handler import error_handler


def http_error_handler(request: Request, exc: HTTPException):
    """
    Handles HTTP erros in the app.

    Parameters:
    request (Request): the request that faced error.
    exc     (Exception): the exception.

    Returns:
    JSON response to best sent as response.
    """
    if exc.detail is None:
        exc.detail = {}
    message = exc.detail.get("message", None) if isinstance(exc.detail, dict) else None
    error_code = (
        exc.detail.get("error_code", ErrorCodes.UNKNOWN_ERROR)
        if isinstance(exc.detail, dict)
        else ErrorCodes.UNKNOWN_ERROR
    )
    detail = exclude_keys_from_dict(exc.detail, ["message", "error_code"])
    detail = add_to_dict(
        detail,
        {
            "path": request.url.path,
            "x-correlation-id": request.headers.get("x-correlation-id"),
        },
    )
    custom_error = error_handler(
        exc=exc,
        error_code=error_code,
        detail=detail,
        message=message,
        status_code=exc.status_code,
    )

    error_response = ErrorDto(
        status_code=exc.status_code,
        message=str(custom_error),
        error_code=custom_error.error_code,
        detail=custom_error.detail,
    )

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump(exclude_none=True),
    )
