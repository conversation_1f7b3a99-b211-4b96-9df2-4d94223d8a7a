from fastapi import Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse


from app.enums.error_enum import ErrorCodes
from app.dtos.error_dto import ErrorDto
from app.handlers.error_handler import error_handler


async def validation_error_handler(request: Request, exc: RequestValidationError):
    """
    Handles validation erros in the app.

    Parameters:
    request (Request): the request that faced error.
    exc     (Exception): the exception.

    Returns:
    JSON response to best sent as response.
    """
    custom_error = error_handler(
        exc=exc,
        error_code=ErrorCodes.VALIDATION_ERROR,
        detail={
            "path": request.url.path,
            "x-correlation-id": request.headers.get("x-correlation-id"),
        },
        message="Input validation failed.",
        status_code=status.HTTP_400_BAD_REQUEST,
    )
    error_response = ErrorDto(
        status_code=status.HTTP_400_BAD_REQUEST,
        error_code=custom_error.error_code,
        message=str(custom_error),
        details=custom_error.detail,
        validation_errors=exc.errors(),
    )
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=error_response.model_dump(exclude_none=True),
    )
