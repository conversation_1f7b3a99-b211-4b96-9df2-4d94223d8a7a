import json
import os
import re
import time
from typing import Any, <PERSON><PERSON>

import vertexai
from vertexai.generative_models import (
    GenerationConfig,
    GenerativeModel,
    HarmBlockThreshold,
    HarmCategory,
    SafetySetting,
)

import app.dtos.risk_item_dto as risk_item_dto
from app.dependencies.logger_dependency import logger
from app.utils.retry_util import gemini_api_retry_decorator
from app.utils.text_cleaner import repair_json, sanitize_text


PROJECT_ID = os.getenv("GOOGLE_PROJECT_ID", "")
safety_settings = [
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
]

vertexai.init(project=PROJECT_ID, location="global")


@gemini_api_retry_decorator
async def call_gemini_async(
    model_name: str,
    assistant_role: str,
    context: str,
    response_structure: str | dict,
    instruction: str,
    top_p: float = 0.2,
    temperature: float = 0.3,
    fewshot_instruction: str | None = None,
    fewshot_dataset: str | dict | None = None,
) -> Tuple[risk_item_dto.RiskItemDto, float, Any]:
    """
    Async Gemini call for processing context with retries.

    Args:
        model_name (str): Name of the generative model to use.
        assistant_role (str): Role of the assistant in the system.
        context (str): Context to be processed by the model.
        response_structure (str or dict): Structure of the response expected from the model.
        instruction (str): Instructions for processing the context.
        fewshot_instruction (str, optional): Few-shot learning instruction. Defaults to None.
        fewshot_dataset (str or dict, optional): Few-shot learning dataset. Defaults to None.

    Returns:
        Tuple[risk_item_dto.RiskItemDto, float, Any] or None:
            - risk_item_dto.RiskItemDto: Extracted risk items as a Data Transfer Object (DTO).
            - float: Runtime of the process.
            - Any: Usage metadata from the model.
            - None: Returns None if an error occurs.
    """
    generation_config = GenerationConfig(
        temperature=temperature,
        top_p=top_p,
        seed=42,
    )

    start_time = time.perf_counter()
    response_text = ""
    system_instruction = f"**Assistant Role:** {assistant_role}\n\n"

    genmodel = GenerativeModel(model_name=model_name, system_instruction=system_instruction)

    user_prompt = [f"**Instruction:** {instruction}"]

    if isinstance(response_structure, dict):
        response_structure = json.dumps(response_structure)

    user_prompt.append(f"**Response Structure:** {response_structure}")

    if fewshot_instruction and fewshot_dataset:
        if isinstance(fewshot_dataset, dict):
            fewshot_dataset = json.dumps(fewshot_dataset)

        user_prompt.append(f"**Fewshot Instruction:** {fewshot_instruction}")
        user_prompt.append(f"**Fewshot Dataset:** {fewshot_dataset}")

    user_prompt.append(f"**The Actual Text:** {context}")

    formated_user_prompt = "\n\n".join(user_prompt)

    try:
        response = genmodel.generate_content(
            contents=formated_user_prompt,
            generation_config=generation_config,
            safety_settings=safety_settings,
        )

        response_text = response.text
        usage_metadata = response.usage_metadata if hasattr(response, "usage_metadata") else None
    except Exception as e:
        logger.error(f"Error processing context using Gemini: {e}")
        raise

    json_data = json.loads("[]")
    try:
        response_text = re.sub(r"```(?:json)?", "", response_text).strip()
        match = re.search(r"\[\s*{[\s\S]*?}\s*\]", response_text)
        response_text = match.group(0) if match else response_text
        response_text = sanitize_text(response_text)
        response_text = repair_json(response_text)
        json_data = json.loads(response_text)
    except json.JSONDecodeError:
        logger.error(f"Error decoding Gemini JSON response: {response_text!r}. Defaulting to []")
    try:
        extracted_risk_items = risk_item_dto.RiskItemDto(risk_items=json_data)
    except Exception as e:
        logger.error(f"Risk item format validation failed: {e}")
        extracted_risk_items = risk_item_dto.RiskItemDto(risk_items=[])
    runtime = time.perf_counter() - start_time
    return extracted_risk_items, runtime, usage_metadata
