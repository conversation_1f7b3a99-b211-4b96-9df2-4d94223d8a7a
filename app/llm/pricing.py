from app.enums.llm_enum import <PERSON>lmModel


def calculate_ai_cost(model_name: str, input_token_count: int, output_token_count: int) -> float:
    """Calculate the cost of using an AI model based on input and output token counts.

    Args:
        model_name (str): The name of the AI model to use.
        input_token_count (int): The number of input tokens.
        output_token_count (int): The number of output tokens.

    Returns:
        float:The total cost in USD.

    Raises:
        ValueError: If the model_name is not supported.
    """
    model_pricing = {
        LlmModel.GEMINI_PRO_15.value: {
            "tiers": [
                {
                    "up_to_tokens": 128000,
                    "input_per_1M_tokens": 1.25,
                    "output_per_1M_tokens": 5.00,
                },
                {
                    "up_to_tokens": float("inf"),
                    "input_per_1M_tokens": 2.50,
                    "output_per_1M_tokens": 10.00,
                },
            ]
        },
        LlmModel.GEMINI_FLASH_20.value: {
            "tiers": [
                {
                    "up_to_tokens": float("inf"),
                    "input_per_1M_tokens": 0.15,
                    "output_per_1M_tokens": 0.60,
                },
            ]
        },
        LlmModel.GEMINI_PRO_25.value: {
            "tiers": [
                {
                    "up_to_tokens": 200000,
                    "input_per_1M_tokens": 1.25,
                    "output_per_1M_tokens": 10.00,
                },
                {
                    "up_to_tokens": float("inf"),
                    "input_per_1M_tokens": 2.50,
                    "output_per_1M_tokens": 15.00,
                },
            ]
        },
        LlmModel.GEMINI_FLASH_25.value: {
            "tiers": [
                {
                    "up_to_tokens": float("inf"),
                    "input_per_1M_tokens": 0.15,
                    "output_per_1M_tokens": 3.5,
                },
            ]
        },
    }

    if model_name not in model_pricing:
        supported_models = ", ".join(model_pricing.keys())
        raise ValueError(f"Unsupported model: {model_name}. Supported models: {supported_models}")

    model_pricing_info = model_pricing[model_name]
    for tier in model_pricing_info["tiers"]:
        if input_token_count <= tier["up_to_tokens"]:
            selected_tier = tier
            break
    input_cost = (input_token_count / 1000000) * selected_tier["input_per_1M_tokens"]
    output_cost = (output_token_count / 1000000) * selected_tier["output_per_1M_tokens"]

    total_cost = input_cost + output_cost
    return total_cost
