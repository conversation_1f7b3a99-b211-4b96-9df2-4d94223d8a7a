from fastapi import <PERSON><PERSON><PERSON>
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from dotenv import load_dotenv
from sqlalchemy.exc import Integrity<PERSON>rror
import uvicorn
from app.utils.get_secrets_from_gcp_util import get_secrets_from_gcp_util


load_dotenv()
get_secrets_from_gcp_util(
    [
        "ENV",
        "DATABASE_URL",
        "RUN_MIGRATIONS",
        "JWT_SECRET_KEY",
        "JWT_ALGORITHM",
        "ACCESS_TOKEN_EXPIRE_MINUTES",
        "SLACK_BOT_TOKEN",
        "SLACK_CHANNEL",
        "GOOGLE_PROJECT_ID",
        "PUBSUB_PROMPT_TOPIC",
        "PUBSUB_RESPONSE_TOPIC",
        "PUBSUB_PROMPT_SUBSCRIPTION",
        "PUBSU<PERSON>_RESPONSE_SUBSCRIPTION",
        "OPENAI_API_KEY",
    ]
)

from app.services.google_messaging_service import (
    PROMPT_SUB,
    PubSubSubscriber,
)
from app.services.outbox_worker_service import OutboxWorker
from app.version import __version__

from app.utils.log_error_to_slack_util import log_error_to_slack
from app.dependencies.custom_exception_dependency import CustomException
from app.dependencies.postgres_database_dependency import (
    create_db_and_tables,
    engine,
    run_migrations,
)
from app.dependencies.logger_dependency import logger
from app.handlers.general_error_handler import general_error_handler
from app.handlers.error_handler import error_handler
from app.handlers.db_error_handler import db_error_handler
from app.handlers.http_error_handler import http_error_handler
from app.handlers.validation_error_handler import validation_error_handler
from app.routers.user_route import router as user_router
from app.routers.test_route import router as test_router
from app.middleware.logger_middleware import LogMiddleware
from app.middleware.db_session_middleware import db_session_middleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    try:
        create_db_and_tables()
        run_migrations()
        openapi_schema = app.openapi()
        security_schemes = {
            "BearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT",  # Optional
            }
        }
        openapi_schema["components"]["securitySchemes"] = security_schemes
        for path in openapi_schema["paths"].values():
            for operation in path.values():
                operation["security"] = [{"BearerAuth": []}]
        app.openapi_schema = openapi_schema
        prompt_sub = PubSubSubscriber(PROMPT_SUB)
        outbox_worker = OutboxWorker(engine=engine)
        
        prompt_sub.start()
        outbox_worker.start()
        
        logger.info("app startup events complete")
        yield
        logger.info("app is shutting down")
        
        outbox_worker.stop()
        prompt_sub.stop()
    except Exception as e:
        error_obj = error_handler(e)
        log_error_to_slack("app is down")
        raise error_obj


app = FastAPI(lifespan=lifespan, version=__version__)

origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.router.prefix = "/api"


# add other handlers here
app.add_exception_handler(IntegrityError, db_error_handler)  # type: ignore
app.add_exception_handler(CustomException, general_error_handler)
app.add_exception_handler(Exception, general_error_handler)
app.add_exception_handler(HTTPException, http_error_handler)  # type: ignore
app.add_exception_handler(RequestValidationError, validation_error_handler)  # type: ignore


# add middlewares here
@app.middleware("http")
async def attach_db_session_middleware(request: Request, call_next):
    return await db_session_middleware(request, call_next)


app.add_middleware(LogMiddleware)


# add each router here
app.include_router(user_router)
app.include_router(test_router)

logger.info("app started")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)
