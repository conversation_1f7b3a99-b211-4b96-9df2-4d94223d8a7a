from fastapi import Request

from app.dependencies.postgres_database_dependency import get_session


async def db_session_middleware(request: Request, call_next):
    """
    Gets database session and stores it in the request state.

    Parameters:
    request (Request): incoming request.
    call_next: function provided by fastapi middleware call.

    Returns:
    Request's response.
    """
    response = None
    try:
        request.state.db = next(get_session())
        response = await call_next(request)
    finally:
        request.state.db.close()
    return response
