import json
import time
import uuid
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.dependencies.logger_dependency import logger
from app.utils.safe_get_json_util import safe_get_json
from app.version import __version__


class LogMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        """
        Logs each request and response information.
        """
        correlation_id = request.headers.get("X-Correlation-ID", str(uuid.uuid4()))
        request.state.correlation_id = correlation_id
        start_time = time.time()
        response = await call_next(request)
        duration = time.time() - start_time
        response.headers["X-Correlation-ID"] = correlation_id
        response.headers["X-Process-Time"] = f"{duration:.4f} seconds"

        response_body = [section async for section in response.body_iterator]
        response_body_bytes = b"".join(response_body)
        response_body_str = response_body_bytes.decode("utf-8")

        logger.info(
            json.dumps(
                {
                    "req": {
                        "correlation_id": correlation_id,
                        "method": request.method,
                        "path": request.url.path,
                        "client_ip": request.client.host,
                        "request_headers": dict(request.headers),
                    },
                    "res": {
                        "correlation_id": correlation_id,
                        "status_code": response.status_code,
                        "path": request.url.path,
                        "response_body": safe_get_json(response_body_str),
                        "response_headers": dict(response.headers),
                    },
                    "duration_in_seconds": f"{duration:.4f}",
                    "type": "api-client-request",
                    "version": __version__,
                }
            )
        )
        # return response
        return Response(
            content=response_body_bytes,
            status_code=response.status_code,
            headers=dict(response.headers),
        )
