from datetime import datetime
from typing import Optional
from sqlmodel import SQLModel, Field, Session
from uuid import uuid4, UUID
from sqlalchemy import event, func, select

from app.utils.get_now_utc_util import get_now_utc


class BasicModel(SQLModel):
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    no: Optional[int] = Field(default=None, sa_column_kwargs={"autoincrement": True})
    created_at: datetime = Field(default_factory=get_now_utc, nullable=False)
    updated_at: datetime = Field(default_factory=get_now_utc, nullable=False)


@event.listens_for(BasicModel, "before_update")
def update_updated_at(mapper, connection, target):
    target.updated_at = get_now_utc()


@event.listens_for(BasicModel, "before_insert")
def before_insert_set_no(mapper, connection, target):
    session = Session(bind=connection)
    max_no = session.exec(select(func.max(BasicModel.no))).scalar()
    target.no = (max_no or 0) + 1
