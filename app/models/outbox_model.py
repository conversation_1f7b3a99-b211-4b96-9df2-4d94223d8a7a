import datetime
import uuid
from typing import Any, ClassVar, Dict, Optional

from sqlalchemy import DateTime, Enum, func
from sqlmodel import JSON, Column, Field, SQLModel

from app.enums.outbox_enum import OutboxStatus


class Outbox(SQLModel, table=True):
    __tablename__: ClassVar[str] = "outbox"
    id: Optional[uuid.UUID] = Field(
        default_factory=uuid.uuid4,
        primary_key=True,
        nullable=False,
    )
    correlation_id: uuid.UUID = Field(foreign_key="requests.id", nullable=False, index=True, ondelete="CASCADE")
    processing_attempts: int = Field(default=0, nullable=False)
    error_message: Optional[str] = Field(default=None)
    payload: Dict[str, Any] = Field(sa_column=Column(JSON, nullable=False))
    status: OutboxStatus = Field(
        sa_column=Column(
            Enum(OutboxStatus, name="outboxstatus", native_enum=True),
            default=OutboxStatus.PENDING,
            nullable=False,
            index=True,
        ),
    )
    available_at: Optional[datetime.datetime] = Field(
        default=None, sa_column=Column(DateTime(timezone=True), nullable=False, index=True, server_default=func.now())
    )
    created_at: Optional[datetime.datetime] = Field(
        default=None, sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    )
    updated_at: Optional[datetime.datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now()),
    )
