from sqlmodel import Field, Relationship
from uuid import UUID

from app.models.basic_model import BasicModel
from app.models.request_model import Request


class Process(BasicModel, table=True):
    __tablename__ = "processes"
    model_id: UUID = Field(foreign_key="language_models.id")
    prompt_id: UUID = Field(foreign_key="prompts.id")
    chunk_text: str = Field(nullable=False)
    result_id: UUID | None = Field(nullable=True)
    result_type: str | None = Field(nullable=True)
    input_tokens: int = Field(nullable=False)
    output_tokens: int | None = Field(nullable=True)
    status: str = Field(index=True)

    request_id: UUID = Field(foreign_key="requests.id", index=True)
    request: Request = Relationship(back_populates="processes")
