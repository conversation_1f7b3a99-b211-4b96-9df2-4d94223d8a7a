from sqlmodel import Field

from app.models.basic_model import BasicModel


class Prompt(BasicModel, table=True):
    __tablename__ = "prompts"
    prompt_type: str = Field(max_length=50, nullable=False, index=True)
    version: str = Field(max_length=50, nullable=False, index=True)
    assistant_role: str = Field(nullable=False)
    instruction: str = Field(nullable=False)
    fewshot_instruction: str = Field(nullable=False)
    response_structure: str = Field(nullable=False)
