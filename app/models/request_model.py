from sqlmodel import Field, Relationship
from uuid import UUID

from app.models.basic_model import BasicModel


class Request(BasicModel, table=True):
    __tablename__ = "requests"
    file_path: str = Field(nullable=False)
    context: str = Field(nullable=False)
    status: str = Field(index=True)
    request_id: UUID = Field(nullable=False)
    rule_set: str = Field(nullable=False)
    cost: float = Field(nullable=True)

    processes: list["Process"] = Relationship(back_populates="request")
