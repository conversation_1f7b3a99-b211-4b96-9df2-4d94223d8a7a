from sqlmodel import Field
from uuid import UUID

from app.models.basic_model import BasicModel


class RiskItem(BasicModel, table=True):
    __tablename__ = "risk_items"
    process_id: UUID = Field(foreign_key="processes.id", index=True)
    violated_rule_id: int | None = Field(nullable=True)
    violation_text: str = Field(nullable=False)
    violation_explanation: str | None = Field(nullable=True)
    verified: bool | None = Field(nullable=True)
    expert_opinion: str | None = Field(nullable=True)
