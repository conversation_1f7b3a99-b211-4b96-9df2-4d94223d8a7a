from fastapi import Depends, APIRouter

from app.dependencies.get_current_user_dependency import get_current_user


router = APIRouter(prefix="/test", tags=["Test"])


@router.get("/public")
async def test_public():
    """
    A test endpoint function.
    """
    return {"message": "Just a test!"}


@router.get("/private")
async def test_private(user: dict = Depends(get_current_user)):
    """
    A test endpoint function.
    """
    return {"message": "Just a test!"}
