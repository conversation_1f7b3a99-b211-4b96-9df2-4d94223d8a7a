from fastapi import APIRouter


import app.dtos.user_dto as user_dto
import app.services.user_service as user_service
from app.dependencies.postgres_database_dependency import SessionDep


router = APIRouter(prefix="/user", tags=["User"])


@router.post("/")
async def create_user(user: user_dto.CreateUserInputDto, db: SessionDep):
    """
    Create new user request
    """
    return await user_service.create_user(user, db)


@router.post("/token", response_model=user_dto.UserToken)
async def login(user: user_dto.UserLogin, db: SessionDep):
    """
    Handles user login based on jwt token.

    Parameters:
    user (UserLogin): uesr data used for login process.
    db (Session): database session.

    Returns:
    JWT token
    """
    return await user_service.login(user, db)
