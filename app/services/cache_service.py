from typing import Optional
from uuid import UUID

from sqlmodel import Session, desc, select

from app.dependencies.logger_dependency import logger
from app.dtos.risk_item_dto import RiskItemDto, SingleRiskItemDto
from app.enums.llm_enum import LlmPromptStatus
from app.models.process_model import Process
from app.models.request_model import Request
from app.models.risk_item_model import RiskItem


async def get_cached_risk_items(
    session: Session,
    request_id: UUID,
    input_text: str,
    rule_set: str,
) -> Optional[RiskItemDto]:
    """
    Checks for existing results for a request. This serves two purposes:
    1.  Caching: Retrieves results from a previous, different request
        that had identical input text and rules.
    2.  Idempotency/Recovery: Retrieves results for the current request_id
        if it has already been processed successfully, preventing re-computation
        on retries.

    The function first searches for a cached result based on content. If none
    is found, it falls back to checking the status of the current request_id.

    Args:
        session (Session): The SQLModel session for database operations.
        request_id (UUID): The ID of the current request, used as a fallback.
        input_text (str): The input text of the request, used to find a
            cached match.
        rule_set (str): The rule set of the request, used to find a
            cached match.

    Returns:
        Optional[RiskItemDto]: A DTO containing the cached risk items if a
        match is found, otherwise None.
    """
    existing_request_id = None

    existing_request = session.exec(
        select(Request)
        .where(
            Request.context == input_text,
            Request.rule_set == rule_set,
            Request.status == LlmPromptStatus.COMPLETED,
        )
        .order_by(desc(Request.created_at))
        .limit(1)
    ).first()

    if existing_request:
        existing_request_id = existing_request.request_id

    target_request_id = existing_request_id or request_id

    existing_process = session.exec(
        select(Process).where(
            Process.request_id == target_request_id,
            Process.status == LlmPromptStatus.COMPLETED,
        )
    ).first()

    if not existing_process:
        return None

    existing_risk_items = session.exec(select(RiskItem).where(RiskItem.process_id == existing_process.id)).all()

    if not existing_risk_items:
        return None

    logger.warning(f"Request with ID {request_id} already processed. Returning existing results.")

    risk_item_dtos = [
        SingleRiskItemDto(
            rule_id=risk_item.violated_rule_id,
            violation_text=risk_item.violation_text,
            violation_explanation=risk_item.violation_explanation,
        )
        for risk_item in existing_risk_items
    ]

    return RiskItemDto(risk_items=risk_item_dtos)
