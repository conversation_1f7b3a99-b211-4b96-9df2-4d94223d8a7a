import asyncio
from typing import Op<PERSON>, <PERSON><PERSON>
from uuid import UUID

from sqlmodel import Session, select

import app.dtos.risk_item_dto as risk_item_dto
from app.dependencies.logger_dependency import logger
from app.dependencies.postgres_database_dependency import engine
from app.dtos.risk_item_dto import RiskItemDto
from app.enums.llm_enum import Llm<PERSON>romptStatus
from app.exceptions.custom_exceptions import PermanentError
from app.llm.gemini import call_gemini_async
from app.llm.pricing import calculate_ai_cost
from app.models.llm_model import LanguageModel
from app.models.process_model import Process
from app.models.prompt_model import Prompt
from app.models.request_model import Request
from app.models.risk_item_model import RiskItem
from app.services.cache_service import get_cached_risk_items
from app.utils.text_util import split_text_into_chunks_with_overlap


async def extract_risk_items_with_gemini(
    session: Session,
    request_id: UUID,
    model_name: str,
    rule_set: str,
    input_text: str,
    prompt_type,
    fewshot_dataset: str | dict | None = None,
) -> <PERSON>ple[Optional[RiskItemDto], int, int, float]:
    """
    Process multiple contexts asynchronously using Gemini.

    Args:
        request_id (UUID): The unique identifier for the request.
        model_name (str): The name of the generative model to use.
        rule_set (str): The set of rules to be applied to the instruction.
        input_text (str): The input text to be processed.
        prompt_type: The type of the prompt to use.
        fewshot_dataset (str or dict, optional): The few-shot learning dataset.

    Returns:
        Tuple[RiskItemDto, int, int, float]: A tuple containing:
            - RiskItemDto: Aggregated risk items from all successful processes.
            - int: Total input token count.
            - int: Total output token count.
            - float: Calculated cost of the LLM call.
    """
    try:
        cached_results = await get_cached_risk_items(
            session=session,
            request_id=request_id,
            input_text=input_text,
            rule_set=rule_set,
        )

        if cached_results:
            await asyncio.sleep(15)  # Simulate some processing time
            return cached_results, 0, 0, 0.00

        union_of_results = RiskItemDto()

        llm_query = select(LanguageModel).where(LanguageModel.model_name == model_name)
        model = session.exec(llm_query).first()

        prompt_query = select(Prompt).where(
            Prompt.prompt_type == prompt_type.value,
        )
        prompt = session.exec(prompt_query).first()
        if not prompt or not model:
            raise PermanentError(f"Prompt or model not found. model {model} prompt {prompt}")

        model_name = model.model_name
        assistant_role = prompt.assistant_role
        instruction = prompt.instruction
        response_structure = prompt.response_structure
        instruction = instruction.format(st_rules=rule_set)
        fewshot_instruction = prompt.fewshot_instruction
        fewshot_instruction = None

        contexts, input_tokens = split_text_into_chunks_with_overlap(input_text, model_name)

        request = session.get(Request, request_id)
        if not request:
            request = Request(
                id=request_id,
                file_path="None",
                context=input_text,
                status=LlmPromptStatus.PROCESSING,
                request_id=request_id,
                rule_set=rule_set,
                cost=0.0,
            )
            session.add(request)
        else:
            request.status = LlmPromptStatus.PROCESSING
            request.context = input_text
            request.rule_set = rule_set

        processes = [
            Process(
                request_id=request_id,
                model_id=model.id,
                prompt_id=prompt.id,
                chunk_text=context,
                input_tokens=input_token,
                status=LlmPromptStatus.PROCESSING,
                result_id=None,
                result_type=None,
                output_tokens=None,
            )
            for context, input_token in zip(contexts, input_tokens)
        ]

        process_ids = []
        active_contexts = []
        for i, process in enumerate(processes):
            existing_process = session.exec(
                select(Process).where(Process.request_id == request_id, Process.chunk_text == process.chunk_text)
            ).first()

            if not existing_process:
                session.add(process)
                session.flush()
                process_ids.append(process.id)
                active_contexts.append(contexts[i])
            else:
                process_ids.append(existing_process.id)
                active_contexts.append(contexts[i])

        tasks = [
            call_gemini_async(
                model_name=model_name,
                assistant_role=assistant_role,
                context=context,
                response_structure=response_structure,
                instruction=instruction,
                fewshot_instruction=fewshot_instruction,
                fewshot_dataset=fewshot_dataset,
            )
            for context in active_contexts
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        input_token_count: int = 0
        output_token_count: int = 0
        successful_tasks_count = 0

        for index, gemini_result in enumerate(results):
            process = session.get(Process, process_ids[index])
            if not process:
                logger.error(f"Process with ID {process_ids[index]} not found in the database.")
                continue

            if isinstance(gemini_result, Exception):
                logger.error(f"Gemini API call failed for process ID {process.id}", exc=gemini_result)
                process.status = LlmPromptStatus.FAILED
                continue

            if isinstance(gemini_result, tuple) and isinstance(gemini_result[0], RiskItemDto):
                try:
                    result, _, usage_metadata = gemini_result
                    union_of_results.risk_items.extend(result.risk_items)

                    if usage_metadata:
                        input_token_count += usage_metadata.prompt_token_count
                        output_token_count += usage_metadata.total_token_count - usage_metadata.prompt_token_count
                        process.output_tokens = usage_metadata.total_token_count - usage_metadata.prompt_token_count

                    process.result_type = "RiskItemDto"
                    process.status = LlmPromptStatus.COMPLETED
                    successful_tasks_count += 1

                    for risk_item in result.risk_items:
                        violation = RiskItem(
                            process_id=process.id,
                            violated_rule_id=risk_item.rule_id,
                            violation_text=risk_item.violation_text or "No violation text",
                            violation_explanation=risk_item.violation_explanation,
                            verified=None,
                            expert_opinion=None,
                        )
                        session.add(violation)
                except Exception as e:
                    logger.error(f"Failed to process Gemini result for process ID {process.id}", exc=e)
                    process.status = LlmPromptStatus.FAILED

        if successful_tasks_count == 0 and len(results) > 0:
            raise PermanentError("All sub-processes failed.")

        request_to_update = session.get_one(Request, request_id)
        request_to_update.status = LlmPromptStatus.COMPLETED
        request_cost: float = calculate_ai_cost(model_name, input_token_count, output_token_count)
        request_to_update.cost = request_cost

        return (
            union_of_results,
            input_token_count,
            output_token_count,
            request_cost,
        )

    except PermanentError as e:
        logger.error(f"Permanent error for request {request_id}: All sub-processes failed.", exc=e)
        request = session.get(Request, request_id)
        if request:
            request.status = LlmPromptStatus.FAILED
        return risk_item_dto.RiskItemDto(risk_items=[]), 0, 0, 0.0
