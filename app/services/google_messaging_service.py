import json
import os
import time
from dataclasses import asdict

from google.api_core.exceptions import DeadlineExceeded
from google.cloud.pubsub_v1 import PublisherClient, SubscriberClient
from google.cloud.pubsub_v1.subscriber.message import Message
from google.cloud.pubsub_v1.types import BatchSettings
from sqlalchemy.exc import IntegrityError
from app.dependencies.postgres_database_dependency import engine

from app.dependencies.logger_dependency import logger
from app.dependencies.postgres_database_dependency import Session
from app.dtos.llm_message_dto import LlmPromptMessageDto
from app.dtos.message_dto import MessageDto
from app.enums.messaging_enum import MessageType
from app.enums.outbox_enum import OutboxStatus
from app.models.outbox_model import Outbox

PROJECT_ID = os.getenv("GOOGLE_PROJECT_ID")
PROMPT_TOPIC = os.getenv("PUBSUB_PROMPT_TOPIC", "llm-prompt")
RESPONSE_TOPIC = os.getenv("PUBSUB_RESPONSE_TOPIC", "llm-response")
PROMPT_SUB = os.getenv("PUBSUB_PROMPT_SUBSCRIPTION", "llm-prompt-sub")
RESPONSE_SUB = os.getenv("PUBSUB_RESPONSE_SUBSCRIPTION", "llm-response-sub")


# services------------------------------------
class PubSubPublisher:
    def __init__(self, topic_name: str):
        self._client = PublisherClient(
            batch_settings=BatchSettings(max_messages=100, max_bytes=5_242_880, max_latency=1)
        )
        self._topic = f"projects/{PROJECT_ID}/topics/{topic_name}"

    def publish(self, msg: MessageDto) -> str:
        try:
            payload = json.dumps(asdict(msg)).encode("utf-8")
            future = self._client.publish(self._topic, payload, correlation_id=msg.id)
            return future.result()
        except Exception as e:
            logger.error(f"Error publishing message: {e}", e)
            raise

    def close(self):
        self._client.transport.close()


class PubSubSubscriber:
    def __init__(self, subscription: str):
        self._client = SubscriberClient()
        self._subscription = f"projects/{PROJECT_ID}/subscriptions/{subscription}"
        self._streaming = None

    def start(self):
        def _callback(message: Message):
            correlation_uuid = None
            try:
                raw_data = json.loads(message.data.decode())
                dto = MessageDto(**raw_data)

                if dto.message_type == MessageType.TEST:
                    message.ack()
                    return

                prompt_payload = LlmPromptMessageDto(**dto.data)
                correlation_uuid = prompt_payload.request_id

                with Session(engine, expire_on_commit=False) as session:
                    try:
                        outbox_item = Outbox(
                            correlation_id=correlation_uuid, payload=dto.data, status=OutboxStatus.PENDING
                        )
                        session.add(outbox_item)
                        session.commit()
                        message.ack()
                        logger.info(f"Successfully queued request {correlation_uuid} to outbox.")

                    except IntegrityError:
                        session.rollback()
                        logger.warning(f"Duplicate request with correlation_id {correlation_uuid} received. Ignoring.")
                        message.ack()

            except Exception as e:
                log_id = f"correlation_id {correlation_uuid}"
                logger.error(f"Unexpected error processing message for {log_id}: {e}", exc=e)

        try:
            logger.info(f"Starting subscriber for {self._subscription}")
            self._streaming = self._client.subscribe(self._subscription, callback=_callback)
        except Exception as e:
            logger.error(f"Error starting subscriber: {e}", exc=e)

    def stop(self):
        try:
            logger.info(f"Stopping subscriber for {self._subscription}")
            if self._streaming:
                self._streaming.cancel()
            self._client.transport.close()
        except Exception as e:
            logger.error(f"Error stopping subscriber: {e}", e)
            raise

    def pull(
        self,
        msg_id: str,
        total_timeout: float = 60.0,
        pull_timeout: float = 3.0,
        max_messages: int = 100,
    ) -> MessageDto | None:
        deadline = time.time() + total_timeout
        logger.info(f"Pulling message with correlation ID: {msg_id}")
        while time.time() < deadline:
            try:
                resp = self._client.pull(
                    subscription=self._subscription,
                    max_messages=max_messages,
                    timeout=pull_timeout,
                )
                for rec in resp.received_messages:
                    msg = rec.message
                    if msg.attributes.get("correlation_id") == msg_id:
                        self._client.acknowledge(
                            subscription=self._subscription,
                            ack_ids=[rec.ack_id],
                        )
                        raw = json.loads(msg.data.decode())
                        return MessageDto(**raw)

            except DeadlineExceeded:
                continue
            except Exception as e:
                logger.error(f"Error pulling message: {e}", e)
                break

        return None
