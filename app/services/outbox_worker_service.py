import asyncio
import datetime

from sqlalchemy.engine import Engine
from sqlmodel import Session, select

from app.dependencies.logger_dependency import logger
from app.dtos.llm_message_dto import LlmPromptMessageDto, LlmResponseMessageDto
from app.dtos.message_dto import MessageDto
from app.enums.messaging_enum import MessageType
from app.exceptions.custom_exceptions import PermanentError
from app.models.outbox_model import Outbox, OutboxStatus
from app.services.gemini_risk_item_service import extract_risk_items_with_gemini
from app.services.google_messaging_service import RESPONSE_TOPIC, PubSubPublisher


class OutboxWorker:
    """
    A robust background worker to process messages from the outbox table.
    It is designed to be resilient to crashes and to handle load gracefully.

    poll_interval: How many seconds the worker should wait before checking the database for new jobs if it finds none. This prevents the worker from constantly hitting the database in a tight loop ("busy-waiting"), which would waste CPU resources.
    batch_size: The maximum number of jobs to fetch from the database at one time. This is your primary tool for rate limiting. By processing only 5 jobs at a time, you prevent sending a sudden, large burst of requests to the Gemini API.
    max_attempts: The number of times the worker will try to process a message before giving up and marking it as FAILED. This prevents "poison pill" messages (messages that will always fail for some reason) from being retried forever.
    processing_timeout: A duration (5 minutes) used to identify "stale" or "stuck" jobs. If a job has been in the PROCESSING state for longer than this timeout, the worker assumes the previous process crashed and will recover it.
    """

    def __init__(
        self,
        engine: Engine,
        poll_interval: float = 10.0,
        batch_size: int = 5,
        max_attempts: int = 3,
        processing_timeout_seconds: int = 5 * 60,
    ):
        self.engine = engine
        self.poll_interval = poll_interval
        self.batch_size = batch_size
        self.max_attempts = max_attempts
        self.processing_timeout = datetime.timedelta(seconds=processing_timeout_seconds)
        self.is_running = False
        self.worker_task: asyncio.Task | None = None

    def start(self):
        """Starts the worker in a background asyncio task."""
        if not self.is_running:
            self.is_running = True
            self.worker_task = asyncio.create_task(self.run_main_loop())
            logger.info("Outbox worker has been started.")

    def stop(self):
        """Stops the worker gracefully."""
        if self.is_running:
            self.is_running = False
            if self.worker_task:
                self.worker_task.cancel()
                self.worker_task = None
            logger.info("Outbox worker has been stopped.")

    async def run_main_loop(self):
        """The main loop of the worker that continuously processes the outbox."""
        publisher = PubSubPublisher(RESPONSE_TOPIC)
        logger.info("Outbox worker main loop is running...")
        while self.is_running:
            try:
                # First, recover any jobs that got stuck from a previous crash.
                await self.recover_stale_messages()

                # Next, fetch a new batch of pending jobs.
                messages = await self.get_pending_messages()

                if not messages:
                    await asyncio.sleep(self.poll_interval)
                    continue

                # Process the batch of jobs concurrently.
                logger.info(f"Fetched {len(messages)} jobs from outbox to process.")
                tasks = [self.process_message(msg, publisher) for msg in messages]
                await asyncio.gather(*tasks)

            except Exception as e:
                logger.error(f"An unexpected error occurred in the worker loop: {e}", exc=e)
                # Avoid busy-looping on persistent errors
                await asyncio.sleep(self.poll_interval)

        publisher.close()

    async def recover_stale_messages(self):
        """Finds and resets jobs that were stuck in a PROCESSING state."""
        with Session(self.engine) as session:
            stale_time = datetime.datetime.now(datetime.timezone.utc) - self.processing_timeout

            stmt = select(Outbox).where(
                Outbox.status == OutboxStatus.PROCESSING,
                Outbox.updated_at < stale_time,  # type: ignore
            )
            stale_messages = session.exec(stmt).all()

            if stale_messages:
                logger.warning(f"Found {len(stale_messages)} stale jobs. Resetting to PENDING.")
                for msg in stale_messages:
                    msg.status = OutboxStatus.PENDING
                    msg.error_message = "Processing timed out. Reset to PENDING."
                session.commit()

    async def get_pending_messages(self) -> list[Outbox]:
        """
        Fetches a batch of pending messages using a row-level lock to ensure
        that multiple worker instances do not process the same message.
        """
        # `expire_on_commit=False` is crucial so objects can be used after the session closes.
        with Session(self.engine, expire_on_commit=False) as session:
            now = datetime.datetime.now(datetime.timezone.utc)

            stmt = (
                select(Outbox)
                .where(
                    Outbox.status == OutboxStatus.PENDING,
                    Outbox.available_at <= now,  # type: ignore
                )
                .order_by(Outbox.created_at)  # type: ignore
                .limit(self.batch_size)
                .with_for_update(skip_locked=True)  # CRITICAL FOR CONCURRENCY
            )

            results = session.exec(stmt).all()
            return list(results)

    async def process_message(self, message: Outbox, publisher: PubSubPublisher):
        """
        Processes a single outbox message using a single, atomic database transaction.
        Either the entire operation succeeds and is committed, or it fails and the
        state is updated for a future retry.
        """
        with Session(self.engine) as session:
            try:
                # Attach the object to this session to track all subsequent changes.
                session.add(message)

                # --- 1. Perform all operations in memory first ---

                # Mark as processing in memory.
                message.status = OutboxStatus.PROCESSING
                message.processing_attempts += 1

                # Call the core business logic (long-running I/O).
                prompt_payload = LlmPromptMessageDto(**message.payload)
                risk_items_dto, input_tokens, output_tokens, cost = await extract_risk_items_with_gemini(
                    session=session,
                    request_id=prompt_payload.request_id,
                    model_name=prompt_payload.model_name.value,
                    rule_set=str(prompt_payload.rule_set),
                    input_text=" ".join(prompt_payload.input_text),
                    prompt_type=prompt_payload.prompt_type,
                )

                if risk_items_dto is None:
                    raise ValueError("extract_risk_items_with_gemini returned None, indicating a processing failure.")

                # If successful, publish the response to Pub/Sub.
                response_data_dto = LlmResponseMessageDto(
                    risk_item_dto=risk_items_dto,
                    input_token_count=input_tokens,
                    output_token_count=output_tokens,
                    submission_cost=cost,
                )
                response_message = MessageDto(
                    message_type=MessageType.LLM_RESPONSE,
                    data=response_data_dto.model_dump(),
                    id=str(message.correlation_id),
                )
                publisher.publish(response_message)
                logger.info(f"Published response for correlation_id: {message.correlation_id}")

                # Finally, mark the job as completed in memory.
                message.status = OutboxStatus.COMPLETED
                logger.info(f"Successfully processed outbox message id: {message.id}")

            except PermanentError as e:
                logger.error(f"Permanent error processing message {message.id}. Moving to FAILED.", exc=e)
                session.add(message)
                message.error_message = f"PermanentError: {e}"
                message.status = OutboxStatus.FAILED  # Immediately fail, do not retry

            except Exception as e:
                # If any part of the 'try' block fails, we land here.
                # The final state of the message will be for a retry or failure.
                logger.error(
                    f"Error processing message {message.id} (correlation_id: {message.correlation_id}): {e}", exc=e
                )

                # Ensure the message object is being tracked by the session before changing it.
                session.add(message)
                message.error_message = str(e)

                if message.processing_attempts >= self.max_attempts:
                    message.status = OutboxStatus.FAILED
                    logger.warning(f"Message {message.id} failed after {self.max_attempts} attempts. Moving to FAILED.")
                else:
                    # Revert to PENDING and set a future 'available_at' time for exponential backoff.
                    message.status = OutboxStatus.PENDING
                    backoff_seconds = self.poll_interval * (2**message.processing_attempts)
                    message.available_at = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
                        seconds=backoff_seconds
                    )
                    logger.info(f"Message {message.id} failed. Retrying in {backoff_seconds:.2f} seconds.")

            finally:
                # --- 2. Commit the final state in a single atomic operation ---
                # This single commit will save the message's final state to the database,
                # whether it's COMPLETED, FAILED, or PENDING for a later retry.
                # This is the core of the transactional safety.
                session.commit()
