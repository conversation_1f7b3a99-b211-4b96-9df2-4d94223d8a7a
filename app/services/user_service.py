from datetime import <PERSON><PERSON><PERSON>

from fastapi import HTT<PERSON>Exception, status
from sqlmodel import Session, select

import app.dtos.user_dto as user_dto
from app.enums.error_enum import ErrorCodes
from app.models.user_model import User
from app.utils.jwt_util import (
    ACCESS_TOKEN_EXPIRE_MINUTES,
    create_access_token,
    get_password_hash,
    verify_password,
)


async def create_user(user: user_dto.CreateUserInputDto, db: Session) -> user_dto.UserDto:
    """
    Handles user registration.

    Parameters:
        user (UserCreate): new user data.
        db: (Session): database session.

    Returns:
        user (UserProfile): Created user data.
    """
    user.password = get_password_hash(user.password)
    db_user = User(**user.model_dump())
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def get_user(user_id: str, db: Session) -> user_dto.UserDto:
    """
    Get user data.

    Parameters:
        user_id (str): user id used for finding user.
        db (Session): database session.

    Returns:
        user (UserDto): User.
    """
    user: User = db.exec(select(User).filter(User.id == user_id)).first()

    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": "Could not find user",
                "error_code": ErrorCodes.NOT_FOUND,
                "user_id": user_id,
            },
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user


async def login(data: user_dto.UserLogin, db: Session) -> user_dto.UserToken:
    """
    Handles user login based on jwt token.

    Parameters:
    user (UserLogin): uesr data used for login process.
    db (Session): database session.

    Returns:
    JWT token
    """
    db_user = db.exec(select(User).where(User.username == data.username)).first()
    if not db_user or not verify_password(data.password, db_user.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "message": "Incorrect email or password",
                "error_code": ErrorCodes.VALIDATION_ERROR,
            },
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(data={"user_id": str(db_user.id)}, expires_delta=access_token_expires)
    return user_dto.UserToken(access_token=access_token)
