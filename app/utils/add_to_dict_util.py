import copy


def add_to_dict(input: dict, data_to_add: dict) -> dict:
    """
    Add data to the extra data to input object.

    Parameters:
    input       (dict): the source dictionary.
    data_to_add (dict): this dictionary's data will be added to source

    Returns:
    Updated dictionary
    """
    if input is None:
        input = {}
    else:
        input = copy.deepcopy(input)
    if input and isinstance(input, dict):
        input.update(data_to_add)
    else:
        input = data_to_add
    return input
