import tempfile

import fitz
from google.cloud import vision_v1
from moviepy import VideoFileClip
from app.dependencies.logger_dependency import logger


def load_file_as_bytes(file_path: str) -> bytes | None:
    """Loads a file from disk into memory as bytes.

    Args:
        file_path: Path to the file.

    Returns:
        File content as bytes, or None if an error occurs.
    """
    try:
        with open(file_path, "rb") as file:
            return file.read()
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        return None
    except Exception as e:
        logger.exception(f"Error reading file: {file_path}", exc_info=e)
        return None


def image_to_text(image_data: bytes) -> str | None:
    """Extracts text from an image using Google Cloud Vision API.

    Args:
        image_data: Image data as bytes.

    Returns:
        Extracted text, or None if an error occurs.
    """
    try:
        client = vision_v1.ImageAnnotatorClient()
        image = vision_v1.Image(content=image_data)
        response = client.text_detection(image=image)

        if response.text_annotations:
            return response.text_annotations[0].description
        else:
            logger.warning("No text found in the image.")
            return None

    except Exception as e:
        logger.exception("Error processing image with Vision API", exc_info=e)
        return None


def video_to_audio(file_path: str) -> str | None:
    """
    Extracts the audio from a video file and saves it as an MP3 file.

    Args:
        file_path (str): The path to the video file.

    Returns:
        str | None: The path to the saved MP3 file if the video has audio,
        otherwise None.
    """
    try:
        video = VideoFileClip(file_path)
        if video.audio is None:
            return None

        audio_file = tempfile.NamedTemporaryFile(suffix=".mp3")
        video.audio.write_audiofile(audio_file.name, codec="mp3", logger=None)
        return audio_file.name

    except Exception as e:
        logger.exception("Error processing video with moviepy", exc_info=e)
        return None


def pdf_to_text(pdf_file: bytes) -> str | None:
    """
    Extracts text from a PDF file page by page and includes text extracted from images.

    Args:
        pdf_file (bytes): The PDF file content in bytes.

    Returns:
        str | None: The extracted text including any text from images, or None if no text is found.
    """
    try:
        text_output = ""
        pdf_document = fitz.open(stream=pdf_file, filetype="pdf")

        for page_num in range(pdf_document.page_count):
            page = pdf_document.load_page(page_num)

            page_text = page.get_text()
            text_output += page_text

            images = page.get_images(full=True)
            for img_index, img in enumerate(images):
                xref = img[0]
                base_image = pdf_document.extract_image(xref)
                image_bytes = base_image["image"]

                image_text = image_to_text(image_bytes)
                if image_text:
                    text_output += "\n" + image_text

        if not text_output.strip():
            return None
        else:
            return text_output

    except Exception as e:
        logger.exception("Error processing pdf with pdf_to_text", exc_info=e)
        return None
