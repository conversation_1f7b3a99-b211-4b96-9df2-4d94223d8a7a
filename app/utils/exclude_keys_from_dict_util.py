def exclude_keys_from_dict(d: dict, keys_to_exclude: list[str]) -> dict:
    """
    Return a new dictionary excluding the specified keys.

    Parameters:
    d (dict): The source dictionary.
    keys_to_exclude (list[str]): list of keys to exclude from the source dictionary.

    Returns:
    A new dictionary with the rest of the keys.
    """
    if isinstance(d, dict):
        return {k: v for k, v in d.items() if k not in keys_to_exclude}
    else:
        return d
