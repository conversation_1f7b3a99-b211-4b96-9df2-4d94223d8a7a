import json
import os
from google.cloud import secretmanager

from app.enums.stage_enum import Stage


def get_secrets_from_gcp_util(secret_names: list[str]):
    """
    Fetch secrets from Google Cloud Secret Manager and set them as environment variables.

    Parameters:
    secret_names (list[str]): List of secret names to fetch from Secret Manager.
    """
    env = os.getenv("ENV", "")
    if env == Stage.LOCAL_DEVELOPMENT.value:
        return
    elif "dev" in env.lower():
        env = "dev"
        secret_id = "finspector-llm-microservice-dev"
    elif "test" in env.lower():
        env = "test"
        secret_id = "finspector-llm-microservice-test"
    elif "main" in env.lower():
        env = "main"
        secret_id = "finspector-llm-microservice-main"
    else:
        raise ValueError(f"Invalid environment '{env}'. Expected one of: dev, test, main.")

    name = f"projects/goodfolio-{env}/secrets/{secret_id}/versions/latest"

    client = secretmanager.SecretManagerServiceClient()
    response = client.access_secret_version(name=name)
    secret_data = response.payload.data.decode("UTF-8")
    secret_dict = json.loads(secret_data)

    for secret_name in secret_names:
        os.environ[secret_name] = str(secret_dict[secret_name])
