import os
from datetime import timed<PERSON><PERSON>
from typing import Optional

import jwt
from dotenv import load_dotenv
from fastapi.security import <PERSON>Auth<PERSON><PERSON><PERSON><PERSON><PERSON>earer
from passlib.context import Crypt<PERSON>ontext

from app.utils.get_now_utc_util import get_now_utc


pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/user/token")

load_dotenv()

JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY")
ALGORITHM = os.getenv("JWT_ALGORITHM")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES"))


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """
    Create JWT access token.

    Parameters:
    data (dict): jwt token payload.
    expires_delta (Optional[timedelta]): when the jwt token should expire.

    Returns:
    JWT token.
    """
    to_encode = data.copy()
    if expires_delta:
        expire = get_now_utc() + expires_delta
    else:
        expire = get_now_utc() + timedelta(minutes=3600)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str):
    """
    Compare and verify plain and hashed passwords against eachother.

    Parameters:
    plain_password (str): plain password.
    hashed_password (str): hashed password.

    Returns:
    Boolean specifying if these 2 passwords match.
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str):
    """
    Get passwrd hashed.

    Parameters:
    password (str): password to hash.

    Returns:
    Hashed password.
    """
    return pwd_context.hash(password)
