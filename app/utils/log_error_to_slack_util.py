import traceback
from slack_sdk import <PERSON><PERSON>lient
from slack_sdk.errors import SlackApiError
import os

from app.dependencies.custom_exception_dependency import CustomException
from app.dependencies.logger_dependency import logger
from app.enums.stage_enum import Stage
from app.utils.get_now_utc_util import get_now_utc

slack_token = os.getenv("SLACK_BOT_TOKEN")
slack_channel = os.getenv("SLACK_CHANNEL")
client = WebClient(token=slack_token)


def log_error_to_slack(error_message: str, error_data: BaseException | None = None):
    """
    Sends an error message and JSON log data to Slack.

    Parameters:
    - error_message (str): Summary of the error.
    - error_data (dict): JSON data containing full error details.
    """
    env = os.getenv("ENV")
    if env == Stage.LOCAL_DEVELOPMENT.value:
        return
    custom_message = f"*Stage:* `{env}`\n*ERROR* - {get_now_utc()}\n\n*Message:* {error_message}\n"
    try:
        if error_data:
            exception_to_analyze = (
                error_data.original_exception if isinstance(error_data, CustomException) else error_data
            )

            if exception_to_analyze:
                tb_exception = traceback.TracebackException.from_exception(exception_to_analyze)
                last_frame = tb_exception.stack[-1] if tb_exception.stack else None

                if last_frame:
                    custom_message += f"*File:* `{last_frame.filename}`\n"
                    custom_message += f"*Line:* `{last_frame.lineno}`\n"
                    custom_message += f"*Function:* `{last_frame.name}`\n"

                error_traceback = "".join(
                    traceback.format_exception(
                        type(exception_to_analyze),
                        exception_to_analyze,
                        exception_to_analyze.__traceback__,
                    )
                )
            client.files_upload_v2(
                channel=slack_channel,
                content=error_traceback,
                filename="error_log.json",
                title="Error Log",
                initial_comment=custom_message,
            )
        else:
            client.chat_postMessage(channel=slack_channel, text=custom_message)
    except SlackApiError as e:
        logger.error(f"Error posting to Slack: {e.response['error']}")
