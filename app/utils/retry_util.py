import tenacity
from google.api_core import exceptions
from tenacity import RetryCallState

from app.dependencies.logger_dependency import logger

wait_normal = tenacity.wait_exponential(multiplier=2, min=1, max=60) + tenacity.wait_random(0, 1)
wait_resource = tenacity.wait_exponential(multiplier=2, min=2, max=60) + tenacity.wait_random(0, 3)


def custom_wait_for_gemini(retry_state: RetryCallState) -> float:
    if retry_state.outcome:
        exc = retry_state.outcome.exception()
        if isinstance(exc, exceptions.ResourceExhausted):
            return wait_resource(retry_state)
    return wait_normal(retry_state)


def log_before_sleep(retry_state: RetryCallState):
    exc = retry_state.outcome.exception() if retry_state.outcome else None
    sleep_time = retry_state.next_action.sleep if retry_state.next_action else 0
    logger.warning(
        f"[Gemini Retry] Attempt {retry_state.attempt_number} failed with {type(exc).__name__ if exc else 'unknown'}."
        f" Retrying in {sleep_time:.1f}s..."
    )


gemini_api_retry_decorator = tenacity.retry(
    stop=tenacity.stop_after_attempt(3),
    wait=custom_wait_for_gemini,
    retry=tenacity.retry_if_exception_type(
        (
            exceptions.ResourceExhausted,
            exceptions.ServiceUnavailable,
            exceptions.InternalServerError,
            exceptions.DeadlineExceeded,
            exceptions.Aborted,
        )
    ),
    before_sleep=log_before_sleep,
    reraise=True,
)
