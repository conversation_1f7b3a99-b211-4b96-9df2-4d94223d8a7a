import json
from typing import Any


def safe_get_json(inp: str | dict) -> Any | str | dict:
    """
    Transforms json string or python dictionaries into json.

    Parameters:
    inp (str | dict): json string or python dictionary.

    Returns:
    Json form of the input if possible otherwise the input itself.
    """
    try:
        if isinstance(inp, dict):
            inp = json.dumps(inp)
        json_message = json.loads(inp)
        return json_message
    except Exception:
        return inp
