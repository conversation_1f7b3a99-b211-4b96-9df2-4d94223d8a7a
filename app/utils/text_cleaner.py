import html
import re
import unicodedata


def sanitize_text(text: str) -> str:
    r"""
    Cleans the input text.

    This function performs several operations to sanitize the input text, including:
    - Decoding hex escape sequences (e.g., \\x41 to 'A').
    - Unescaping HTML entities (e.g., &amp; to &).
    - Normalizing whitespace to single spaces.
    - Removing unwanted control characters, while allowing \n, \t, and \r.
    - Replacing literal backslash + apostrophe (\\') with just an apostrophe (').

    Args:
        text (str): The input text that needs to be cleaned.

    Returns:
        str: The sanitized text after cleaning.
    """

    # Decode hex escape sequences like \x41
    text = re.sub(r"\\x([0-9A-Fa-f]{2})", lambda match: chr(int(match.group(1), 16)), text)

    # Convert HTML entities to characters
    text = html.unescape(text)

    # Normalize whitespace
    text = re.sub(r"\s+", " ", text).strip()

    # Remove non-printable control characters except allowed ones
    allowed_controls = {"\n", "\t", "\r"}
    text = "".join(ch for ch in text if unicodedata.category(ch)[0] != "C" or ch in allowed_controls)

    # Remove emojis
    emoji_pattern = re.compile(
        "["
        "\U0001f600-\U0001f64f"  # Emoticons
        "\U0001f300-\U0001f5ff"  # Symbols & pictographs
        "\U0001f680-\U0001f6ff"  # Transport & map symbols
        "\U0001f1e0-\U0001f1ff"  # Flags
        "\U00002700-\U000027bf"  # Dingbats
        "\U0001f900-\U0001f9ff"  # Supplemental Symbols
        "\U00002600-\U000026ff"  # Misc symbols
        "]+",
        flags=re.UNICODE,
    )

    text = emoji_pattern.sub(r"", text)

    # Replace "\\'" (literal backslash + apostrophe in the string) with just "'"
    text = text.replace("\\'", "'")

    return text


def repair_json(text: str) -> str:
    """
    Attempts to repair common formatting issues in loosely structured JSON-like strings.

    Args:
        text (str): A potentially malformed JSON-like string.

    Returns:
        str: A corrected version of the input string that is closer to valid JSON.
    """
    # Replace : '"…'  →  : "…"
    text = re.sub(r":\s*'\"(.*?)'", r': "\1"', text, flags=re.VERBOSE)
    return text
