import re
from typing import <PERSON><PERSON>

from vertexai.preview import tokenization

from app.enums.llm_enum import <PERSON>lm<PERSON>odel


def split_text_into_chunks_with_overlap(
    text: str, model_name: str, max_tokens_per_chunk: int = 12800, overlap: int = 256
) -> Tuple[list[str], list[int]]:
    """
    Splits a given text into chunks of a specified maximum token length with overlap.

    Args:
        text (str): The text to be split into chunks.
        model_name (str): The name of the model for tokenization.
        max_tokens_per_chunk (int, optional): The maximum number of tokens allowed per chunk. Defaults to 1250.
        overlap (int, optional): The number of tokens to overlap between chunks. Defaults to 250.

    Returns:
        list[str]: A list of text chunks.
        list[int]: A list of token counts for each chunk.

    This function tokenizes the input text and splits it into chunks, ensuring that the total
    number of tokens in each chunk does not exceed `max_tokens_per_chunk`. It also ensures
    an overlap of `overlap` tokens between consecutive chunks.
    """
    # Since the vertexai tokenizer does not yet support recent models like Gemini 2.5,
    # I've implemented a fallback to Gemini 1.5.
    if model_name != LlmModel.GEMINI_PRO_15.value:
        model_name = LlmModel.GEMINI_PRO_15.value

    sentences = extract_sentences_from_text(text)
    chunks: list[str] = []
    token_counts: list[int] = []
    current_chunk: str = ""
    current_token_count: int = 0

    if not sentences:
        return chunks, token_counts

    tokenizer = tokenization.get_tokenizer_for_model(model_name)

    for sentence in sentences:
        potential_chunk, potential_tokens = _build_potential_chunk(current_chunk, sentence, tokenizer)

        if potential_tokens <= max_tokens_per_chunk:
            current_chunk = potential_chunk
            current_token_count = potential_tokens
        else:
            if current_chunk:
                chunks.append(current_chunk)
                token_counts.append(current_token_count)

            current_chunk = _build_chunk_with_overlap(sentence, chunks, overlap)
            current_token_count = tokenizer.count_tokens(current_chunk).total_tokens

    if current_chunk:
        chunks.append(current_chunk)
        token_counts.append(current_token_count)

    return chunks, token_counts


def extract_sentences_from_text(text: str) -> list[str]:
    """
    Splits text into sentence-like units using regex.

    This function attempts to identify sentence boundaries based on common
    punctuation (.?!) while trying to avoid splitting on abbreviations
    (e.g., "Mr.", "e.g."). Each extracted sentence is stripped of
    leading/trailing whitespace, and empty strings are filtered out.

    Args:
        text (str): The input text to be segmented into sentences.

    Returns:
        list[str]: A list of extracted and cleaned sentences.
                   Returns an empty list if the input text is empty or
                   contains no discernible sentences based on the pattern.
    """
    if not text or not text.strip():
        return []
    pattern = r"""
        (?<!\w\.\w\.)        # Negative lookbehind: not an abbreviation like "e.g."
        (?<![A-Z][a-z]\.)    # Negative lookbehind: not a name abbreviation like "Dr."
        (?<=[.?!])           # Positive lookbehind: must end with ., ?, or !
        \s+                  # Split at the whitespace following the punctuation
    """
    sentences = re.split(pattern, text, flags=re.VERBOSE)
    return sentences


def _build_potential_chunk(current_chunk: str, sentence: str, tokenizer):
    potential_chunk: str
    if not current_chunk:
        potential_chunk = sentence
    else:
        potential_chunk = current_chunk + " " + sentence
    potential_tokens: int = tokenizer.count_tokens(potential_chunk).total_tokens
    return potential_chunk, potential_tokens


def _build_chunk_with_overlap(sentence: str, chunks: list[str], overlap: int):
    overlap_segment: str = ""
    if chunks and overlap > 0:
        last_finalized_chunk_text: str = chunks[-1]
        words_from_last_chunk: list[str] = last_finalized_chunk_text.split()
        if words_from_last_chunk:
            overlap_word_count: int = min(overlap, len(words_from_last_chunk))
            selected_overlap_words: list[str] = words_from_last_chunk[-overlap_word_count:]
            overlap_segment = " ".join(selected_overlap_words)
    if overlap_segment:
        current_chunk = overlap_segment + " " + sentence
    else:
        current_chunk = sentence
    return current_chunk
