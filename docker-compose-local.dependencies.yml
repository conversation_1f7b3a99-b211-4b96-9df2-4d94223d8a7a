services:
  postgres:
    image: postgres:14.17-bookworm
    container_name: postgres-db
    environment:
      POSTGRES_USER: userpostgres
      POSTGRES_PASSWORD: c4HgtsgtZNXgzdPr
      POSTGRES_DB: mlapi
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  pgadmin:
    image: dpage/pgadmin4:9.3.0
    container_name: pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5433:80"
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres-data:
    driver: local