{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "\n", "from dotenv import load_dotenv\n", "\n", "sys.path.append(\"/home/<USER>/work/finspector-ml-api\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'goodfolio-dev'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["load_dotenv(\"../app/.env\")\n", "PROJECT_ID = os.getenv(\"GOOGLE_PROJECT_ID\", \"\")\n", "PROJECT_ID"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from app.enums.llm_enum import LlmModel\n", "from app.llm.gemini import call_gemini_async"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assistant_role = \"\"\"You are a meticulous **Compliance Violation Detector (CVD)**, an automated audit module. Your reputation for flawless accuracy is paramount, as your output is fed directly into a downstream system where errors in identifying violations can lead to significant regulatory risk.\"\"\"\n", "\n", "instruction = \"\"\"\n", "I will provide:\n", "\n", "RULES (JSON array of objects with `rule_id` and `rule_text`).\n", "\n", "Given these RULES:\n", "\n", "{st_rules}\n", "  \n", "You MUST specify verbatim substring from the text that violates the rule, where possible.\n", "If a violation is due to the absence of required information (e.g., a missing disclaimer), the `violation_text` should be the relevant sentence or clause that lacks the required information.\n", "A single rule can be violated multiple times; you must report every instance.\n", "\n", "* Audience: The final JSON you generate is for a machine. It must be a perfectly structured JSON array, with no extra text, explanations, or formatting errors. The response must start with `[` and end with `]`.\n", "* Knowledge Boundary: You must perform your analysis using only the provided RULES and the **The Actual Text**. DO NOT use any external knowledge about financial regulations or infer meaning beyond what is explicitly stated.\n", "\n", "You must analyze **The Actual Text**, testing its content against every rule. A violation object should only be created if it PASSES the following test.\n", "\n", "The Litmus Test Question: \"Does the provided text contain a specific phrase, or does its overall tone and balance, present a potential breach of this rule’s intent?\"\n", "\n", "*  Examples that PASS the test (and should be INCLUDED):**\n", "    *  Rule: \"Prohibit the use of the word ‘guaranteed’.\"\n", "    *  Text: \"Join our fund and get a ‘guaranteed’ 12% return.\"\n", "    *  Result: This is a clear violation. The verbatim text \"guaranteed 12% return\" is undeniable evidence.\n", "\n", "*  Example that FAILS the test (and should be DISCARDED):**\n", "    *  Rule: \"Prohibit unrealistic or promissory claims of future performance.\"\n", "    *  Text: \"Our quarterly market analysis is now available for download.\"\n", "    *  Result: This fails the test. The text is purely informational and contains no claim of performance, promise, or benefit whatsoever. It is entirely irrelevant to the rule, so no violation should be logged.\n", "\n", "Before generating the final JSON, you MUST meticulously follow this internal reasoning process for each potential violation. \n", "This process is for your reasoning only and **MUST NOT** appear in the final output.\n", "\n", "1.  Initial Scan: Read **The Actual Text** and compare it to a specific rule from RULES.\n", "2.  Draft Finding: If a potential violation is found, apply the Litmus Test. Draft a finding, ensuring you have identified the exact `violation_text`.\n", "3.  Self-Correction (The Auditor’s Check): Adopt the mindset of a skeptical senior auditor. Review your drafted finding:\n", "    *   Evidence Check: \"Is the `violation_text` I’ve recorded *truly verbatim* from the source? Or did I paraphrase?\" If it's not verbatim, correct it if possible.\n", "    *   Violation Check: \"Does it constitute a breach of the rule's intent?\" If not, discard the finding.\n", "4.  Finalize Violation List: After checking the entire text against all rules, compile the list of confirmed violations that passed the Auditor’s Check.    \n", "\"\"\"\n", "\n", "st_rules = \"\"\"\n", "[\n", "  {\n", "    \"rule_id\": 1,\n", "    \"rule_text\": \"Prohibit the use of the words 'guaranteed,' 'protected,' or 'secure' when describing investment performance or returns.\"\n", "  },\n", "  {\n", "    \"rule_id\": 2,\n", "    \"rule_text\": \"Check for presence of 'capital at risk' or similar wording.\"\n", "  },\n", "  {\n", "    \"rule_id\": 3,\n", "    \"rule_text\": \"Only if a specific investment product is discussed, ensure the promotion includes an explanation of all applicable fees.\"\n", "  },\n", "  {\n", "    \"rule_id\": 4,\n", "    \"rule_text\": \"Ensure the promotion states the name of the firm communicating the product.\"\n", "  },\n", "  {\n", "    \"rule_id\": 5,\n", "    \"rule_text\": \"Ensure the promotional material is crafted to responsibly showcase the product, highlighting its potential benefits while transparently disclosing any drawbacks.\"\n", "  },\n", "  {\n", "    \"rule_id\": 6,\n", "    \"rule_text\": \"Identify any potentially misleading information in the promotion.\"\n", "  }\n", "]\"\"\"\n", "instruction = instruction.format(st_rules=st_rules)\n", "\n", "response_structure = \"\"\"\n", "Your final response must be **a single, valid JSON array** containing all confirmed violation objects. \n", "If no violations are found, the output must be an empty array `[]`.\n", "\n", "Response MUST strictly comply to this format:\n", "\n", "**Format**:\n", "[\n", "  {\n", "    \"rule_id\": \"exact ID from the violated rule (integer)\",\n", "    \"violation_text\": \"verbatim substring (if possible) from the promotion that demonstrates the violation (string)\",\n", "    \"violation_explanation\": \"concise explanation linking the violation_text to why it breaches the rule\"\n", "  },\n", "  {\n", "    \"rule_id\": \"exact ID from the violated rule (integer)\",\n", "    \"violation_text\": \"verbatim substring (if possible) from the promotion that demonstrates the violation (string)\",\n", "    \"violation_explanation\": \"concise explanation linking the violation_text to why it breaches the rule\"\n", "  },\n", "  {\n", "    \"rule_id\": \"exact ID from the violated rule (integer)\",\n", "    \"violation_text\": \"verbatim substring (if possible) from the promotion that demonstrates the violation (string)\",\n", "    \"violation_explanation\": \"concise explanation linking the violation_text to why it breaches the rule\"\n", "  }\n", "]\n", "\n", "**Few-Shot Examples**:\n", "\n", "- Example 1\n", "RULES:\n", "[\n", "    {\n", "        \"rule_id\": 1,\n", "        \"rule_text\": \"Prohibit the use of the words ‘guaranteed,’ ‘protected,’ or ‘secure’ when describing investment performance or returns.\"\n", "    }\n", "]\n", "The Actual Text:\n", "\"Invest with us for secure retirement income.\"\n", "Output:\n", "[\n", "    {\n", "        \"rule_id\": 1,\n", "        \"violation_text\": \"secure retirement income\",\n", "        \"violation_explanation\": \"The text uses the prohibited word ‘secure’ when describing investment income, directly violating Rule 1.\"\n", "    }\n", "]\n", "\n", "- Example 2\n", "RULES:\n", "[\n", "    {\n", "        \"rule_id\": 4,\n", "        \"rule_text\": \"Ensure the promotion states the name of the firm communicating the product.\"\n", "    }\n", "]\n", "The Actual Text:\n", "\"Get great returns with our amazing investment opportunity!\"\n", "Output:\n", "[\n", "    {\n", "        \"rule_id\": 4,\n", "        \"violation_text\": \"Get great returns with our amazing investment opportunity!\",\n", "        \"violation_explanation\": \"The promotion does not mention the name of the firm communicating the product, violating Rule 4.\"\n", "    }\n", "]\n", "\n", "- Example 3:\n", "RULES:\n", "[\n", "    {\n", "        \"rule_id\": 3,\n", "        \"rule_text\": \"Only if a specific investment product is discussed, ensure the promotion includes an explanation of all applicable fees.\"\n", "    },\n", "    {\n", "        \"rule_id\": 5,\n", "        \"rule_text\": \"Ensure the promotional material is crafted to responsibly showcase the product, highlighting its potential benefits while transparently disclosing any drawbacks.\"\n", "    }\n", "]\n", "The Actual Text: \n", "\"Invest in our ‘AlphaGrowth Fund’ for potentially high returns! Limited spots available. Double your investment!\"\n", "Output:\n", "[\n", "    {\n", "        \"rule_id\": 3,\n", "        \"violation_text\": \"Invest in our ‘AlphaGrowth Fund’ for potentially high returns! Limited spots available. Double your investment!\",\n", "        \"violation_explanation\": \"The promotion discusses a specific investment product (‘AlphaGrowth Fund’) but fails to include any explanation of applicable fees, violating Rule 3.\"\n", "    },\n", "    {\n", "        \"rule_id\": 5,\n", "        \"violation_text\": \"Invest in our ‘AlphaGrowth Fund’ for potentially high returns! Limited spots available. Double your investment!\",\n", "        \"violation_explanation\": \"The promotion highlights potential benefits (‘potentially high returns’, ‘Double your investment’) without disclosing any drawbacks of the AlphaGrowth Fund, making it unbalanced and violating Rule 5.\"\n", "    }\n", "]\n", "\n", "- Example 4\n", "RULES:\n", "[\n", "    {\n", "        \"rule_id\": 5,\n", "        \"rule_text\": \"Ensure the promotional material is crafted to responsibly showcase the product, highlighting its potential benefits while transparently disclosing any drawbacks.\"\n", "    },\n", "    {\n", "        \"rule_id\": 6,\n", "        \"rule_text\": \"Identify any potentially misleading information in the promotion.\"\n", "    }\n", "]\n", "The Actual Text: \n", "\"How to Invest in Water: 4 Ideas for Intelligent Investors. To kickstart, we have compiled 4 ideas on how to invest in water: Water investment opportunities, <PERSON>’s take on water, Water stocks, and Water ETFs.\"\n", "Output:\n", "[\n", "    {\n", "        \"rule_id\": 5,\n", "        \"violation_text\": \"To kickstart, we have compiled 4 ideas on how to invest in water: Water investment opportunities, <PERSON>’s take on water, Water stocks, and Water ETFs.\",\n", "        \"violation_explanation\": \"The material promotes investment ideas by listing opportunities (stocks, ETFs) but fails to transparently disclose any of the inherent risks or potential drawbacks, creating an unbalanced and irresponsible presentation.\"\n", "    },\n", "    {\n", "        \"rule_id\": 6,\n", "        \"violation_text\": \"How to Invest in Water: 4 Ideas for Intelligent Investors\",\n", "        \"violation_explanation\": \"The phrase ‘Intelligent Investors’ is potentially misleading flattery. It implies that engaging with the content is a sign of intelligence, which could cause a reader to lower their critical assessment of the material.\"\n", "    }\n", "]\n", "\"\"\"\n", "\n", "\n", "context = \"\"\"\n", "[\"\"How Wise are our Wisers? We grilled our team with some tough travel questions and boggled their minds  Fastest train? Not Japan. Busiest airport? Not JFK. But if you guessed Bangkok's full ceremonial name, you nailed it  #Quiz #Travel #TravelQuiz #OfficeChat #Voxpops How Wise are our Wisers? We grilled our team with some tough travel questions and boggled their minds  Fastest train? Not Japan. Busiest airport? Not JFK. But if you guessed Bangkok's full ceremonial name, you nailed it  #Quiz #Travel #TravelQuiz #OfficeChat #Voxpops Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. That's a very simple question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. Yeah, that's too hard. How Wise are our Wisers? We grilled our team with some tough travel questions and boggled their minds  Fastest train? Not Japan. Busiest airport? Not JFK. But if you guessed Bangkok's full ceremonial name, you nailed it  #Quiz #Travel #TravelQuiz #OfficeChat #Voxpops How Wise are our Wisers? We grilled our team with some tough travel questions and boggled their minds  Fastest train? Not Japan. Busiest airport? Not JFK. But if you guessed Bangkok's full ceremonial name, you nailed it  #Quiz #Travel #TravelQuiz #OfficeChat #Voxpops Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. That's a very simple question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. That's a very simple question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. That's a very simple question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. Yeah, that's too hard. Which city has the longest name in its native language? Bangkok (continuous laughter) Bangkok. Oh my god, how did you get that guess? Tallinn. Tallinn. Beijing. Is it German? No. Okay, let's try Singapore. Istanbul. Istanbul? No. It's Atlanta. Artsville Jackson. I know this one. London. No. New York. Not New York. It's Cotland. Europe. Dubai. No, it's not. Is it China? Yes, it is. China. Yes, oh my god. Japan. Japan. Japan. Japan. Trick question, trick question. No? That's a very tough question. That's a very simple question. Yeah, that's too hard.\"\"]\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["gemini-2.5-pro\n"]}], "source": ["model_name = LlmModel.GEMINI_PRO_25.value\n", "print(model_name)\n", "result = await call_gemini_async(\n", "    model_name=model_name,\n", "    assistant_role=assistant_role,\n", "    context=context,\n", "    response_structure=response_structure,\n", "    instruction=instruction,\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["31.542839864000143\n", "1\n", "4\n", "How Wise are our Wisers? We grilled our team with some tough travel questions and boggled their minds\n", "The promotion does not explicitly state the name of the firm communicating the product. While it uses the term 'Wisers', this is presented as a pun and not a clear identification of the company.\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["extracted_risk_items, runtime, usage_metadata = result\n", "print(runtime)\n", "print(len(extracted_risk_items.risk_items))\n", "for risk_item in extracted_risk_items.risk_items:\n", "    print(risk_item.rule_id)\n", "    print(risk_item.violation_text)\n", "    print(risk_item.violation_explanation)\n", "    print(\"-\" * 100)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input 4817\n", "Ouput 101\n", "Thinking 2863\n", "Cost 0.03566125\n"]}], "source": ["from app.llm.pricing import calculate_ai_cost\n", "\n", "input_token_count = usage_metadata.prompt_token_count\n", "output_token_count = usage_metadata.candidates_token_count\n", "thinking_token_count = usage_metadata.total_token_count - (\n", "    usage_metadata.prompt_token_count + usage_metadata.candidates_token_count\n", ")\n", "print(\"Input\", input_token_count)\n", "print(\"Ouput\", output_token_count)\n", "print(\"Thinking\", thinking_token_count)\n", "print(\"Cost\", calculate_ai_cost(model_name, input_token_count, output_token_count + thinking_token_count))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "UPDATE prompts\n", "SET\n", "    assistant_role = 'You are a meticulous **Compliance Violation Detector (CVD)**, an automated audit module. Your reputation for flawless accuracy is paramount, as your output is fed directly into a downstream system where errors in identifying violations can lead to significant regulatory risk.',\n", "    instruction = '\n", "I will provide:\n", "\n", "RULES (JSON array of objects with `rule_id` and `rule_text`).\n", "\n", "Given these RULES:\n", "\n", "{st_rules}\n", "\n", "You MUST specify verbatim substring from the text that violates the rule, where possible.\n", "If a violation is due to the absence of required information (e.g., a missing disclaimer), the `violation_text` should be the entire relevant sentence or clause that lacks the required information.\n", "A single rule can be violated multiple times; you must report every instance.\n", "\n", "* Audience: The final JSON you generate is for a machine. It must be a perfectly structured JSON array, with no extra text, explanations, or formatting errors. The response must start with `[` and end with `]`.\n", "* Knowledge Boundary: You must perform your analysis using only the provided RULES and the **The Actual Text**. DO NOT use any external knowledge about financial regulations or infer meaning beyond what is explicitly stated.\n", "\n", "You must analyze **The Actual Text**, testing its content against every rule. A violation object should only be created if it PASSES the following test.\n", "\n", "The Litmus Test Question: \"Does the provided text contain a specific phrase, or does its overall tone and balance, present a potential breach of this rule’s intent?\"\n", "\n", "*  Examples that PASS the test (and should be INCLUDED):**\n", "    *  Rule: \"Prohibit the use of the word ‘guaranteed’.\"\n", "    *  Text: \"Join our fund and get a ‘guaranteed’ 12% return.\"\n", "    *  Result: This is a clear violation. The verbatim text \"guaranteed 12% return\" is undeniable evidence.\n", "\n", "*  Example that FAILS the test (and should be DISCARDED):**\n", "    *  Rule: \"Prohibit unrealistic or promissory claims of future performance.\"\n", "    *  Text: \"Our quarterly market analysis is now available for download.\"\n", "    *  Result: This fails the test. The text is purely informational and contains no claim of performance, promise, or benefit whatsoever. It is entirely irrelevant to the rule, so no violation should be logged.\n", "\n", "Before generating the final JSON, you MUST meticulously follow this internal reasoning process for each potential violation. \n", "This process is for your reasoning only and **MUST NOT** appear in the final output.\n", "\n", "1.  Initial Scan: Read **The Actual Text** and compare it to a specific rule from RULES.\n", "2.  Draft Finding: If a potential violation is found, apply the Litmus Test. Draft a finding, ensuring you have identified the exact `violation_text`.\n", "3.  Self-Correction (The Auditor’s Check): Adopt the mindset of a skeptical senior auditor. Review your drafted finding:\n", "    *   Evidence Check: \"Is the `violation_text` I’ve recorded *truly verbatim* from the source? Or did I paraphrase?\" If it’s not verbatim, correct it if possible.\n", "    *   Violation Check: \"Does it constitute a breach of the rule’s intent?\" If not, discard the finding.\n", "4.  Finalize Violation List: After checking the entire text against all rules, compile the list of confirmed violations that passed the Auditor’s Check.    \n", "',\n", "    response_structure = '\n", "Your final response must be **a single, valid JSON array** containing all confirmed violation objects. \n", "If no violations are found, the output must be an empty array `[]`.\n", "\n", "Response MUST strictly comply to this format:\n", "\n", "**Format**:\n", "[\n", "  {\n", "    \"rule_id\": \"exact ID from the violated rule (integer)\",\n", "    \"violation_text\": \"verbatim substring (if possible) from the promotion that demonstrates the violation (string)\",\n", "    \"violation_explanation\": \"concise explanation linking the violation_text to why it breaches the rule\"\n", "  },\n", "  {\n", "    \"rule_id\": \"exact ID from the violated rule (integer)\",\n", "    \"violation_text\": \"verbatim substring (if possible) from the promotion that demonstrates the violation (string)\",\n", "    \"violation_explanation\": \"concise explanation linking the violation_text to why it breaches the rule\"\n", "  },\n", "  {\n", "    \"rule_id\": \"exact ID from the violated rule (integer)\",\n", "    \"violation_text\": \"verbatim substring (if possible) from the promotion that demonstrates the violation (string)\",\n", "    \"violation_explanation\": \"concise explanation linking the violation_text to why it breaches the rule\"\n", "  }\n", "]\n", "\n", "**Few-Shot Examples**:\n", "\n", "- Example 1\n", "RULES:\n", "[\n", "    {\n", "        \"rule_id\": 1,\n", "        \"rule_text\": \"Prohibit the use of the words ‘guaranteed,’ ‘protected,’ or ‘secure’ when describing investment performance or returns.\"\n", "    }\n", "]\n", "The Actual Text:\n", "\"Invest with us for secure retirement income.\"\n", "Output:\n", "[\n", "    {\n", "        \"rule_id\": 1,\n", "        \"violation_text\": \"secure retirement income\",\n", "        \"violation_explanation\": \"The text uses the prohibited word ‘secure’ when describing investment income, directly violating Rule 1.\"\n", "    }\n", "]\n", "\n", "- Example 2\n", "RULES:\n", "[\n", "    {\n", "        \"rule_id\": 4,\n", "        \"rule_text\": \"Ensure the promotion states the name of the firm communicating the product.\"\n", "    }\n", "]\n", "The Actual Text:\n", "\"Get great returns with our amazing investment opportunity!\"\n", "Output:\n", "[\n", "    {\n", "        \"rule_id\": 4,\n", "        \"violation_text\": \"Get great returns with our amazing investment opportunity!\",\n", "        \"violation_explanation\": \"The promotion does not mention the name of the firm communicating the product, violating Rule 4.\"\n", "    }\n", "]\n", "\n", "- Example 3:\n", "RULES:\n", "[\n", "    {\n", "        \"rule_id\": 3,\n", "        \"rule_text\": \"Only if a specific investment product is discussed, ensure the promotion includes an explanation of all applicable fees.\"\n", "    },\n", "    {\n", "        \"rule_id\": 5,\n", "        \"rule_text\": \"Ensure the promotional material is crafted to responsibly showcase the product, highlighting its potential benefits while transparently disclosing any drawbacks.\"\n", "    }\n", "]\n", "The Actual Text: \n", "\"Invest in our ‘AlphaGrowth Fund’ for potentially high returns! Limited spots available. Double your investment!\"\n", "Output:\n", "[\n", "    {\n", "        \"rule_id\": 3,\n", "        \"violation_text\": \"Invest in our ‘AlphaGrowth Fund’ for potentially high returns! Limited spots available. Double your investment!\",\n", "        \"violation_explanation\": \"The promotion discusses a specific investment product (‘AlphaGrowth Fund’) but fails to include any explanation of applicable fees, violating Rule 3.\"\n", "    },\n", "    {\n", "        \"rule_id\": 5,\n", "        \"violation_text\": \"Invest in our ‘AlphaGrowth Fund’ for potentially high returns! Limited spots available. Double your investment!\",\n", "        \"violation_explanation\": \"The promotion highlights potential benefits (‘potentially high returns’, ‘Double your investment’) without disclosing any drawbacks of the AlphaGrowth Fund, making it unbalanced and violating Rule 5.\"\n", "    }\n", "]\n", "\n", "- Example 4\n", "RULES:\n", "[\n", "    {\n", "        \"rule_id\": 5,\n", "        \"rule_text\": \"Ensure the promotional material is crafted to responsibly showcase the product, highlighting its potential benefits while transparently disclosing any drawbacks.\"\n", "    },\n", "    {\n", "        \"rule_id\": 6,\n", "        \"rule_text\": \"Identify any potentially misleading information in the promotion.\"\n", "    }\n", "]\n", "The Actual Text: \n", "\"How to Invest in Water: 4 Ideas for Intelligent Investors. To kickstart, we have compiled 4 ideas on how to invest in water: Water investment opportunities, <PERSON>’s take on water, Water stocks, and Water ETFs.\"\n", "Output:\n", "[\n", "    {\n", "        \"rule_id\": 5,\n", "        \"violation_text\": \"To kickstart, we have compiled 4 ideas on how to invest in water: Water investment opportunities, <PERSON>’s take on water, Water stocks, and Water ETFs.\",\n", "        \"violation_explanation\": \"The material promotes investment ideas by listing opportunities (stocks, ETFs) but fails to transparently disclose any of the inherent risks or potential drawbacks, creating an unbalanced and irresponsible presentation.\"\n", "    },\n", "    {\n", "        \"rule_id\": 6,\n", "        \"violation_text\": \"How to Invest in Water: 4 Ideas for Intelligent Investors\",\n", "        \"violation_explanation\": \"The phrase ‘Intelligent Investors’ is potentially misleading flattery. It implies that engaging with the content is a sign of intelligence, which could cause a reader to lower their critical assessment of the material.\"\n", "    }\n", "]\n", "',\n", "    updated_at = CURRENT_TIMESTAMP\n", "WHERE\n", "    id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';\n", "\n"]}], "source": ["# Python code to help prepare the strings for SQL\n", "# You'll take the output of these print statements for your SQL query\n", "\n", "assistant_role = \"\"\"You are a meticulous **Compliance Violation Detector (CVD)**, an automated audit module. Your reputation for flawless accuracy is paramount, as your output is fed directly into a downstream system where errors in identifying violations can lead to significant regulatory risk.\"\"\"\n", "\n", "instruction = \"\"\"\n", "I will provide:\n", "\n", "RULES (JSON array of objects with `rule_id` and `rule_text`).\n", "\n", "Given these RULES:\n", "\n", "{st_rules}\n", "  \n", "You MUST specify verbatim substring from the text that violates the rule, where possible.\n", "If a violation is due to the absence of required information (e.g., a missing disclaimer), the `violation_text` should be the entire relevant sentence or clause that lacks the required information.\n", "A single rule can be violated multiple times; you must report every instance.\n", "\n", "* Audience: The final JSON you generate is for a machine. It must be a perfectly structured JSON array, with no extra text, explanations, or formatting errors. The response must start with `[` and end with `]`.\n", "* Knowledge Boundary: You must perform your analysis using only the provided RULES and the **The Actual Text**. DO NOT use any external knowledge about financial regulations or infer meaning beyond what is explicitly stated.\n", "\n", "You must analyze **The Actual Text**, testing its content against every rule. A violation object should only be created if it PASSES the following test.\n", "\n", "The Litmus Test Question: \"Does the provided text contain a specific phrase, or does its overall tone and balance, present a potential breach of this rule’s intent?\"\n", "\n", "*  Examples that PASS the test (and should be INCLUDED):**\n", "    *  Rule: \"Prohibit the use of the word ‘guaranteed’.\"\n", "    *  Text: \"Join our fund and get a ‘guaranteed’ 12% return.\"\n", "    *  Result: This is a clear violation. The verbatim text \"guaranteed 12% return\" is undeniable evidence.\n", "\n", "*  Example that FAILS the test (and should be DISCARDED):**\n", "    *  Rule: \"Prohibit unrealistic or promissory claims of future performance.\"\n", "    *  Text: \"Our quarterly market analysis is now available for download.\"\n", "    *  Result: This fails the test. The text is purely informational and contains no claim of performance, promise, or benefit whatsoever. It is entirely irrelevant to the rule, so no violation should be logged.\n", "\n", "Before generating the final JSON, you MUST meticulously follow this internal reasoning process for each potential violation. \n", "This process is for your reasoning only and **MUST NOT** appear in the final output.\n", "\n", "1.  Initial Scan: Read **The Actual Text** and compare it to a specific rule from RULES.\n", "2.  Draft Finding: If a potential violation is found, apply the Litmus Test. Draft a finding, ensuring you have identified the exact `violation_text`.\n", "3.  Self-Correction (The Auditor’s Check): Adopt the mindset of a skeptical senior auditor. Review your drafted finding:\n", "    *   Evidence Check: \"Is the `violation_text` I’ve recorded *truly verbatim* from the source? Or did I paraphrase?\" If it’s not verbatim, correct it if possible.\n", "    *   Violation Check: \"Does it constitute a breach of the rule’s intent?\" If not, discard the finding.\n", "4.  Finalize Violation List: After checking the entire text against all rules, compile the list of confirmed violations that passed the Auditor’s Check.    \n", "\"\"\"\n", "\n", "response_structure = \"\"\"\n", "Your final response must be **a single, valid JSON array** containing all confirmed violation objects. \n", "If no violations are found, the output must be an empty array `[]`.\n", "\n", "Response MUST strictly comply to this format:\n", "\n", "**Format**:\n", "[\n", "  {\n", "    \"rule_id\": \"exact ID from the violated rule (integer)\",\n", "    \"violation_text\": \"verbatim substring (if possible) from the promotion that demonstrates the violation (string)\",\n", "    \"violation_explanation\": \"concise explanation linking the violation_text to why it breaches the rule\"\n", "  },\n", "  {\n", "    \"rule_id\": \"exact ID from the violated rule (integer)\",\n", "    \"violation_text\": \"verbatim substring (if possible) from the promotion that demonstrates the violation (string)\",\n", "    \"violation_explanation\": \"concise explanation linking the violation_text to why it breaches the rule\"\n", "  },\n", "  {\n", "    \"rule_id\": \"exact ID from the violated rule (integer)\",\n", "    \"violation_text\": \"verbatim substring (if possible) from the promotion that demonstrates the violation (string)\",\n", "    \"violation_explanation\": \"concise explanation linking the violation_text to why it breaches the rule\"\n", "  }\n", "]\n", "\n", "**Few-Shot Examples**:\n", "\n", "- Example 1\n", "RULES:\n", "[\n", "    {\n", "        \"rule_id\": 1,\n", "        \"rule_text\": \"Prohibit the use of the words ‘guaranteed,’ ‘protected,’ or ‘secure’ when describing investment performance or returns.\"\n", "    }\n", "]\n", "The Actual Text:\n", "\"Invest with us for secure retirement income.\"\n", "Output:\n", "[\n", "    {\n", "        \"rule_id\": 1,\n", "        \"violation_text\": \"secure retirement income\",\n", "        \"violation_explanation\": \"The text uses the prohibited word ‘secure’ when describing investment income, directly violating Rule 1.\"\n", "    }\n", "]\n", "\n", "- Example 2\n", "RULES:\n", "[\n", "    {\n", "        \"rule_id\": 4,\n", "        \"rule_text\": \"Ensure the promotion states the name of the firm communicating the product.\"\n", "    }\n", "]\n", "The Actual Text:\n", "\"Get great returns with our amazing investment opportunity!\"\n", "Output:\n", "[\n", "    {\n", "        \"rule_id\": 4,\n", "        \"violation_text\": \"Get great returns with our amazing investment opportunity!\",\n", "        \"violation_explanation\": \"The promotion does not mention the name of the firm communicating the product, violating Rule 4.\"\n", "    }\n", "]\n", "\n", "- Example 3:\n", "RULES:\n", "[\n", "    {\n", "        \"rule_id\": 3,\n", "        \"rule_text\": \"Only if a specific investment product is discussed, ensure the promotion includes an explanation of all applicable fees.\"\n", "    },\n", "    {\n", "        \"rule_id\": 5,\n", "        \"rule_text\": \"Ensure the promotional material is crafted to responsibly showcase the product, highlighting its potential benefits while transparently disclosing any drawbacks.\"\n", "    }\n", "]\n", "The Actual Text: \n", "\"Invest in our ‘AlphaGrowth Fund’ for potentially high returns! Limited spots available. Double your investment!\"\n", "Output:\n", "[\n", "    {\n", "        \"rule_id\": 3,\n", "        \"violation_text\": \"Invest in our ‘AlphaGrowth Fund’ for potentially high returns! Limited spots available. Double your investment!\",\n", "        \"violation_explanation\": \"The promotion discusses a specific investment product (‘AlphaGrowth Fund’) but fails to include any explanation of applicable fees, violating Rule 3.\"\n", "    },\n", "    {\n", "        \"rule_id\": 5,\n", "        \"violation_text\": \"Invest in our ‘AlphaGrowth Fund’ for potentially high returns! Limited spots available. Double your investment!\",\n", "        \"violation_explanation\": \"The promotion highlights potential benefits (‘potentially high returns’, ‘Double your investment’) without disclosing any drawbacks of the AlphaGrowth Fund, making it unbalanced and violating Rule 5.\"\n", "    }\n", "]\n", "\n", "- Example 4\n", "RULES:\n", "[\n", "    {\n", "        \"rule_id\": 5,\n", "        \"rule_text\": \"Ensure the promotional material is crafted to responsibly showcase the product, highlighting its potential benefits while transparently disclosing any drawbacks.\"\n", "    },\n", "    {\n", "        \"rule_id\": 6,\n", "        \"rule_text\": \"Identify any potentially misleading information in the promotion.\"\n", "    }\n", "]\n", "The Actual Text: \n", "\"How to Invest in Water: 4 Ideas for Intelligent Investors. To kickstart, we have compiled 4 ideas on how to invest in water: Water investment opportunities, <PERSON>’s take on water, Water stocks, and Water ETFs.\"\n", "Output:\n", "[\n", "    {\n", "        \"rule_id\": 5,\n", "        \"violation_text\": \"To kickstart, we have compiled 4 ideas on how to invest in water: Water investment opportunities, <PERSON>’s take on water, Water stocks, and Water ETFs.\",\n", "        \"violation_explanation\": \"The material promotes investment ideas by listing opportunities (stocks, ETFs) but fails to transparently disclose any of the inherent risks or potential drawbacks, creating an unbalanced and irresponsible presentation.\"\n", "    },\n", "    {\n", "        \"rule_id\": 6,\n", "        \"violation_text\": \"How to Invest in Water: 4 Ideas for Intelligent Investors\",\n", "        \"violation_explanation\": \"The phrase ‘Intelligent Investors’ is potentially misleading flattery. It implies that engaging with the content is a sign of intelligence, which could cause a reader to lower their critical assessment of the material.\"\n", "    }\n", "]\n", "\"\"\"\n", "\n", "\n", "print(f\"\"\"\n", "UPDATE prompts\n", "SET\n", "    assistant_role = '{assistant_role}',\n", "    instruction = '{instruction}',\n", "    response_structure = '{response_structure}',\n", "    updated_at = CURRENT_TIMESTAMP\n", "WHERE\n", "    id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ { \"rule_id\": 250, \"violation_text\": \"It’s hard to play well, but it’s easy to work hard” Congrats KD on entering the NBA top all-time regular season scoring list. Continue to be legendary <PERSON> Work. Easy Money. PayPal PHX 35 Just Do It. 2 Hard Work. Easy Money. PayPal PHX 35 Just Do It. 2\", \"violation_explanation\": \"The letter 'B' appears 4 times in the text.\" } ]\n"]}], "source": ["import json\n", "import re\n", "\n", "from app.utils.text_cleaner import repair_json, sanitize_text\n", "\n", "response_text = '[ { \"rule_id\": 250, \"violation_text\": \\'\"It’s hard to play well, but it’s easy to work hard” Congrats <PERSON><PERSON> on entering the NBA top all-time regular season scoring list. Continue to be legendary <PERSON> Work. Easy Money. PayPal PHX 35 Just Do It. 2 Hard Work. Easy Money. PayPal PHX 35 Just Do It. 2\\', \"violation_explanation\": \"The letter \\'B\\' appears 4 times in the text.\" } ]'\n", "\n", "response_text = re.sub(r\"```(?:json)?\", \"\", response_text).strip()\n", "match = re.search(r\"\\[\\s*{[\\s\\S]*?}\\s*\\]\", response_text)\n", "response_text = match.group(0) if match else response_text\n", "response_text = sanitize_text(response_text)\n", "response_text = repair_json(response_text)\n", "print(response_text)\n", "json_data = json.loads(response_text)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initializing Vertex AI for project 'goodfolio-dev' in 'global'...\n", "Sending prompt: 'Hello, what is your name?'\n", "\n", "--- Response ---\n", "I am a large language model, trained by Google. I don't have a name in the traditional sense. You can just call me Google's AI, or nothing at all! How can I help you today?\n", "\n", "----------------------------------------\n"]}], "source": ["import os\n", "\n", "import vertexai\n", "from vertexai.generative_models import GenerativeModel\n", "\n", "PROJECT_ID = os.getenv(\"GOOGLE_PROJECT_ID\")\n", "\n", "LOCATION = \"global\"\n", "SIMPLE_PROMPT = \"Hello, what is your name?\"\n", "\n", "\n", "async def main():\n", "    try:\n", "        print(f\"Initializing Vertex AI for project '{PROJECT_ID}' in '{LOCATION}'...\")\n", "        vertexai.init(project=PROJECT_ID, location=LOCATION)\n", "\n", "        model = GenerativeModel(model_name=LlmModel.GEMINI_FLASH_20.value)\n", "\n", "        print(f\"Sending prompt: '{SIMPLE_PROMPT}'\")\n", "\n", "        response = model.generate_content(SIMPLE_PROMPT)\n", "        print(\"\\n--- Response ---\")\n", "        print(response.text)\n", "    except Exception as e:\n", "        print(f\"An error occurred during the Vertex AI test: {e}\")\n", "    finally:\n", "        print(\"-\" * 40)\n", "\n", "\n", "await main()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(['How Wise are our Wisers? We grilled our team with some tough travel '\n", "  'questions and boggled their minds  Fastest train?'],\n", " [25])\n", "(['How Wise are our Wisers?',\n", "  'are our Wisers? We grilled our team with some tough travel questions and '\n", "  'boggled their minds  Fastest train?'],\n", " [7, 23])\n"]}], "source": ["from app.utils.text_util import split_text_into_chunks_with_overlap\n", "import pprint\n", "text = \"How Wise are our Wisers? We grilled our team with some tough travel questions and boggled their minds  Fastest train?\"\n", "\n", "result = split_text_into_chunks_with_overlap(text = text, model_name=LlmModel.GEMINI_FLASH_20, max_tokens_per_chunk=12800, overlap=256)\n", "pprint.pprint(result)\n", "result = split_text_into_chunks_with_overlap(text = text, model_name=LlmModel.GEMINI_FLASH_20, max_tokens_per_chunk=10, overlap=3)\n", "pprint.pprint(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 2}