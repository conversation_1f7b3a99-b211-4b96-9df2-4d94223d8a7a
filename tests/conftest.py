import datetime
import json
import uuid
from collections.abc import Generator
from typing import Any

import pytest
from dotenv import load_dotenv
from sqlalchemy import Engine
from sqlmodel import Session, SQLModel, create_engine

# Import all models to ensure they are registered with SQLModel
from app.models import (  # noqa: F401
    llm_model,
    outbox_model,
    process_model,
    prompt_model,
    request_model,
    risk_item_model,
    user_model,
)


def custom_serializer(obj: Any) -> str:
    """Custom JSON serializer for objects not serializable by default json.dumps."""
    if isinstance(obj, uuid.UUID):
        return str(obj)
    if isinstance(obj, (datetime.datetime, datetime.date)):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")


@pytest.fixture(scope="session", autouse=True)
def load_env() -> None:
    """Load environment variables from the .env file before any tests run."""
    load_dotenv("../app/.env")


@pytest.fixture(name="engine")
def engine_fixture() -> Generator[Engine, None, None]:
    """Provides a clean, in-memory SQLite database engine for each test function."""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        json_serializer=lambda obj: json.dumps(obj, default=custom_serializer),
    )
    SQLModel.metadata.create_all(engine)
    yield engine
    SQLModel.metadata.drop_all(engine)


@pytest.fixture(name="session")
def session_fixture(engine: Engine) -> Generator[Session, None, None]:
    """Provides a session for a given engine."""
    with Session(engine) as session:
        yield session
