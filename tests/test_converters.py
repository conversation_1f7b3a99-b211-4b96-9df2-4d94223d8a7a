from pathlib import Path
from unittest.mock import AN<PERSON>, <PERSON><PERSON>, patch

import pytest

from app.utils.converters import (
    image_to_text,
    load_file_as_bytes,
    pdf_to_text,
    video_to_audio,
)


@pytest.fixture
def mock_logger(mocker):
    """Fixture to mock the logger in the converters module."""
    return mocker.patch("app.utils.converters.logger")


class TestLoadFileAsBytes:
    """Test suite for the load_file_as_bytes function."""

    def test_load_existing_file(self, tmp_path: Path):
        """Should return the content of the file as bytes."""
        file_path = tmp_path / "test_file.txt"
        file_path.write_bytes(b"test content")

        result = load_file_as_bytes(str(file_path))

        assert result == b"test content"

    def test_load_nonexistent_file(self, mock_logger: <PERSON><PERSON>):
        """Should return None and log an error if the file does not exist."""
        result = load_file_as_bytes("nonexistent_file.txt")

        assert result is None
        mock_logger.error.assert_called_once_with("File not found: nonexistent_file.txt")

    def test_load_file_with_exception(self, mocker: <PERSON><PERSON>, mock_logger: Mock, tmp_path: Path):
        """Should return None and log an exception if an error occurs while reading the file."""
        mocker.patch("builtins.open", side_effect=Exception("Test Exception"))
        file_path = tmp_path / "test_file.txt"

        result = load_file_as_bytes(str(file_path))

        assert result is None
        mock_logger.exception.assert_called_once_with(f"Error reading file: {file_path}", exc_info=ANY)


@patch("app.utils.converters.vision_v1.ImageAnnotatorClient")
class TestImageToText:
    """Test suite for the image_to_text function."""

    def test_image_to_text_success(self, mock_client: Mock):
        """Should return the extracted text from the image."""
        mock_response = Mock()
        mock_response.text_annotations = [Mock(description="test text")]
        mock_client.return_value.text_detection.return_value = mock_response

        result = image_to_text(b"test image data")

        assert result == "test text"

    def test_image_to_text_no_text(self, mock_client: Mock, mock_logger: Mock):
        """Should return None and log a warning if no text is found."""
        mock_response = Mock()
        mock_response.text_annotations = []
        mock_client.return_value.text_detection.return_value = mock_response

        result = image_to_text(b"test image data")

        assert result is None
        mock_logger.warning.assert_called_once_with("No text found in the image.")

    def test_image_to_text_api_error(self, mock_client: Mock, mock_logger: Mock):
        """Should return None and log an exception if the Vision API call fails."""
        mock_client.return_value.text_detection.side_effect = Exception("API Error")

        result = image_to_text(b"test image data")

        assert result is None
        mock_logger.exception.assert_called_once_with("Error processing image with Vision API", exc_info=ANY)


@patch("app.utils.converters.VideoFileClip")
class TestVideoToAudio:
    """Test suite for the video_to_audio function."""

    def test_video_to_audio_with_audio(self, mock_videofileclip: Mock):
        """Should return audio data when the video has an audio track."""
        mock_audio = Mock()
        mock_video = Mock()
        mock_video.audio = mock_audio
        mock_videofileclip.return_value = mock_video

        result = video_to_audio("test_video.mp4")

        assert result is not None
        mock_videofileclip.assert_called_once_with("test_video.mp4")
        mock_audio.write_audiofile.assert_called_once()

    def test_video_to_audio_no_audio(self, mock_videofileclip: Mock):
        """Should return None when the video has no audio track."""
        mock_video = Mock()
        mock_video.audio = None
        mock_videofileclip.return_value = mock_video

        result = video_to_audio("test_video.mp4")

        assert result is None
        mock_videofileclip.assert_called_once_with("test_video.mp4")

    def test_video_to_audio_exception(self, mock_videofileclip: Mock, mock_logger: Mock):
        """Should return None and log an exception if moviepy fails."""
        mock_videofileclip.side_effect = Exception("MoviePy Error")

        result = video_to_audio("test_video.mp4")

        assert result is None
        mock_videofileclip.assert_called_once_with("test_video.mp4")
        mock_logger.exception.assert_called_once_with("Error processing video with moviepy", exc_info=ANY)


@patch("app.utils.converters.fitz.open")
class TestPdfToText:
    """Test suite for the pdf_to_text function."""

    def test_pdf_to_text_with_text_and_image(self, mock_fitz_open: Mock, mocker: Mock):
        """Should return combined text from PDF content and embedded images."""
        mock_page = Mock()
        mock_page.get_text.return_value = "Some text from PDF"
        # Simulate one image found on the page
        mock_page.get_images.return_value = [(1, 0, 100, 100, 8, "DeviceRGB", "", "img0", "XObject")]

        mock_doc = Mock()
        mock_doc.page_count = 1
        mock_doc.load_page.return_value = mock_page
        # Mock the extraction of the image bytes
        mock_doc.extract_image.return_value = {"image": b"image data"}
        mock_fitz_open.return_value = mock_doc

        mock_image_to_text = mocker.patch(
            "app.utils.converters.image_to_text",
            return_value="Some text from image",
        )

        result = pdf_to_text(b"test pdf data")

        assert result == "Some text from PDF\nSome text from image"
        mock_image_to_text.assert_called_once_with(b"image data")

    def test_pdf_to_text_no_text_or_images(self, mock_fitz_open: Mock):
        """Should return None if the PDF contains no text or images."""
        mock_page = Mock()
        mock_page.get_text.return_value = ""
        mock_page.get_images.return_value = []  # No images

        mock_doc = Mock()
        mock_doc.page_count = 1
        mock_doc.load_page.return_value = mock_page
        mock_fitz_open.return_value = mock_doc

        result = pdf_to_text(b"test pdf data")

        assert result is None

    def test_pdf_to_text_exception(self, mock_fitz_open: Mock, mock_logger: Mock):
        """Should return None and log an exception if PyMuPDF (fitz) fails."""
        mock_fitz_open.side_effect = Exception("PyMuPDF Error")

        result = pdf_to_text(b"test pdf data")

        assert result is None
        mock_logger.exception.assert_called_once_with("Error processing pdf with pdf_to_text", exc_info=ANY)
