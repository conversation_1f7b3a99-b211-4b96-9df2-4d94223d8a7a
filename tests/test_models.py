from uuid import uuid4

import pytest
from sqlmodel import Session

from app.enums.llm_enum import <PERSON>lmModel, LlmPromptStatus, PromptType
from app.models.llm_model import LanguageModel
from app.models.process_model import Process
from app.models.prompt_model import Prompt
from app.models.request_model import Request
from app.models.risk_item_model import RiskItem
from app.models.user_model import User


@pytest.fixture
def language_model(session: Session) -> LanguageModel:
    """Provides a committed LanguageModel instance for tests."""
    lm = LanguageModel(
        model_name=LlmModel.GEMINI_PRO_15,
        input_token_cost=0.0035 / 1000,
        output_token_cost=0.0105 / 1000,
    )
    session.add(lm)
    session.commit()
    session.refresh(lm)
    return lm


@pytest.fixture
def prompt(session: Session) -> Prompt:
    """Provides a committed Prompt instance for tests."""
    p = Prompt(
        prompt_type=PromptType.FILE,
        version="1.0",
        assistant_role="You are a helpful assistant.",
        instruction="Analyze the following text for compliance risks.",
        fewshot_instruction="Here is an example of a compliant text...",
        response_structure="JSON",
    )
    session.add(p)
    session.commit()
    session.refresh(p)
    return p


@pytest.fixture
def request_model(session: Session) -> Request:
    """Provides a committed Request instance for tests."""
    req = Request(
        file_path="/uploads/test_document.pdf",
        context="Contract review for ACME Corp.",
        status=LlmPromptStatus.CREATED,
        request_id=uuid4(),
        rule_set="FINRA-2210",
        cost=0.0,
    )
    session.add(req)
    session.commit()
    session.refresh(req)
    return req


@pytest.fixture
def process(session: Session, language_model: LanguageModel, prompt: Prompt, request_model: Request) -> Process:
    """Provides a committed Process instance for tests."""
    proc = Process(
        model_id=language_model.id,
        prompt_id=prompt.id,
        request_id=request_model.id,
        chunk_text="This is an example chunk of text to be processed.",
        result_id=uuid4(),
        result_type="ANALYSIS",
        input_tokens=50,
        output_tokens=150,
        status=LlmPromptStatus.COMPLETED,
    )
    session.add(proc)
    session.commit()
    session.refresh(proc)
    return proc


def test_create_and_retrieve_language_model(session: Session):
    """Tests the creation and retrieval of a LanguageModel instance."""
    language_model = LanguageModel(
        model_name=LlmModel.GEMINI_FLASH_25,
        input_token_cost=0.1,
        output_token_cost=0.2,
    )
    session.add(language_model)
    session.commit()
    session.refresh(language_model)
    retrieved_model = session.get(LanguageModel, language_model.id)
    assert retrieved_model is not None
    assert retrieved_model.model_name == LlmModel.GEMINI_FLASH_25


def test_create_and_retrieve_prompt(session: Session):
    """Tests the creation and retrieval of a Prompt instance."""
    prompt = Prompt(
        prompt_type=PromptType.TICKET,
        version="1.1",
        assistant_role="assistant role",
        instruction="test instruction",
        fewshot_instruction="test fewshot instruction",
        response_structure="test response structure",
    )
    session.add(prompt)
    session.commit()
    session.refresh(prompt)
    retrieved_prompt = session.get(Prompt, prompt.id)
    assert retrieved_prompt is not None
    assert retrieved_prompt.version == "1.1"
    assert retrieved_prompt.prompt_type == PromptType.TICKET


def test_create_and_retrieve_request(session: Session):
    """Tests the creation and retrieval of a Request instance."""
    request = Request(
        context="test context",
        status=LlmPromptStatus.PROCESSING,
        request_id=uuid4(),
        file_path="file_path",
        rule_set="None",
        cost=0.5,
    )
    session.add(request)
    session.commit()
    session.refresh(request)
    retrieved_request = session.get(Request, request.id)
    assert retrieved_request is not None
    assert retrieved_request.status == LlmPromptStatus.PROCESSING


def test_create_and_retrieve_process(
    session: Session, language_model: LanguageModel, prompt: Prompt, request_model: Request
):
    """Tests the creation and retrieval of a Process instance using fixtures."""
    process = Process(
        model_id=language_model.id,
        prompt_id=prompt.id,
        request_id=request_model.id,
        chunk_text="example_chunk",
        result_id=uuid4(),
        result_type="test_type",
        input_tokens=10,
        output_tokens=5,
        status=LlmPromptStatus.PROCESSING,
    )
    session.add(process)
    session.commit()
    session.refresh(process)
    retrieved_process = session.get(Process, process.id)
    assert retrieved_process is not None
    assert retrieved_process.status == LlmPromptStatus.PROCESSING
    assert retrieved_process.request_id == request_model.id


def test_create_and_retrieve_risk_item(session: Session, process: Process):
    """Tests the creation and retrieval of a RiskItem instance using a fixture."""
    risk_item = RiskItem(
        process_id=process.id,
        violated_rule_id=1,
        violation_text="test violation",
        violation_explanation="test explanation",
        verified=False,
        expert_opinion="No opinion provided.",
    )
    session.add(risk_item)
    session.commit()
    session.refresh(risk_item)
    retrieved_risk_item = session.get(RiskItem, risk_item.id)
    assert retrieved_risk_item is not None
    assert retrieved_risk_item.process_id == process.id
    assert retrieved_risk_item.violation_text == "test violation"
    assert retrieved_risk_item.verified is False


def test_create_and_retrieve_user(session: Session):
    """Tests the creation and retrieval of a User instance."""
    user = User(username="testuser", password="password")
    session.add(user)
    session.commit()
    session.refresh(user)
    retrieved_user = session.get(User, user.id)
    assert retrieved_user is not None
    assert retrieved_user.username == "testuser"
