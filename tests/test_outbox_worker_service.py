import asyncio
import datetime
import uuid
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
from sqlalchemy import Engine
from sqlmodel import Session

from app.dtos.llm_message_dto import LlmPromptMessageDto
from app.dtos.risk_item_dto import RiskItemDto
from app.enums.llm_enum import <PERSON>lmModel, PromptType
from app.enums.outbox_enum import OutboxStatus
from app.exceptions.custom_exceptions import PermanentError
from app.models.outbox_model import Outbox
from app.services.outbox_worker_service import OutboxWorker


@pytest.fixture(name="worker")
def worker_fixture(engine: Engine):
    """Provides an OutboxWorker instance with a short poll interval for faster tests."""
    return OutboxWorker(engine=engine, poll_interval=0.1, batch_size=5, max_attempts=3)


@pytest.fixture(name="mock_publisher")
def mock_publisher_fixture():
    """Mocks the PubSubPublisher."""
    with patch("app.services.outbox_worker_service.PubSubPublisher") as mock_pub_sub:
        mock_instance = MagicMock()
        mock_instance.publish = MagicMock()
        mock_pub_sub.return_value = mock_instance
        yield mock_instance


@pytest.fixture(name="mock_gemini_service")
def mock_gemini_service_fixture():
    """Mocks the extract_risk_items_with_gemini function."""
    with patch(
        "app.services.outbox_worker_service.extract_risk_items_with_gemini", new_callable=AsyncMock
    ) as mock_gemini:
        yield mock_gemini


def create_outbox_message(
    session: Session,
    status: OutboxStatus = OutboxStatus.PENDING,
    processing_attempts: int = 0,
    updated_at: datetime.datetime | None = None,
    available_at: datetime.datetime | None = None,
    created_at: datetime.datetime | None = None,
) -> Outbox:
    """Helper function to create and save an outbox message."""
    now = datetime.datetime.now(datetime.timezone.utc)
    payload = LlmPromptMessageDto(
        request_id=uuid.uuid4(),
        input_text=["Sample text"],
        rule_set=[{"rule_id": 1}],
        model_name=LlmModel.GEMINI_FLASH_20,
        prompt_type=PromptType.FILE,
    )
    message = Outbox(
        correlation_id=payload.request_id,
        payload=payload.model_dump(),
        status=status,
        processing_attempts=processing_attempts,
        created_at=created_at or now,
        updated_at=updated_at or now,
        available_at=available_at or now,
    )
    session.add(message)
    session.commit()
    session.refresh(message)
    session.expunge(message)
    return message


@pytest.mark.asyncio
async def test_process_message_success(
    session: Session, worker: OutboxWorker, mock_publisher: MagicMock, mock_gemini_service: AsyncMock
):
    """
    Tests the ideal scenario: a pending message is processed successfully,
    the result is published, and the message is marked as COMPLETED.
    """
    message = create_outbox_message(session)
    message_id = message.id
    mock_gemini_service.return_value = (RiskItemDto(risk_items=[]), 10, 20, 0.001)

    await worker.process_message(message, mock_publisher)

    processed_message = session.get(Outbox, message_id)
    assert processed_message is not None
    assert processed_message.status == OutboxStatus.COMPLETED
    assert processed_message.error_message is None
    assert processed_message.processing_attempts == 1
    mock_publisher.publish.assert_called_once()
    mock_gemini_service.assert_awaited_once()


@pytest.mark.asyncio
async def test_process_message_permanent_error(
    session: Session, worker: OutboxWorker, mock_publisher: MagicMock, mock_gemini_service: AsyncMock
):
    """
    Tests that a message is moved to FAILED state immediately upon a PermanentError.
    """
    message = create_outbox_message(session)
    message_id = message.id
    error = PermanentError("This is a permanent failure.")
    mock_gemini_service.side_effect = error

    await worker.process_message(message, mock_publisher)

    processed_message = session.get(Outbox, message_id)
    assert processed_message is not None
    assert processed_message.status == OutboxStatus.FAILED
    assert processed_message.error_message is not None
    assert "PermanentError" in processed_message.error_message
    assert processed_message.processing_attempts == 1
    mock_publisher.publish.assert_not_called()


@pytest.mark.asyncio
@patch("app.services.outbox_worker_service.datetime")
async def test_process_message_transient_error_retry(
    mock_datetime_module: MagicMock,
    session: Session,
    worker: OutboxWorker,
    mock_publisher: MagicMock,
    mock_gemini_service: AsyncMock,
):
    """
    Tests that a transient error results in a retry attempt with exponential backoff.
    """
    message = create_outbox_message(session)
    message_id = message.id
    error = ValueError("A temporary network issue.")
    mock_gemini_service.side_effect = error

    start_time = datetime.datetime.now(datetime.timezone.utc)
    mock_datetime_module.datetime.now.return_value = start_time
    mock_datetime_module.timedelta = datetime.timedelta

    await worker.process_message(message, mock_publisher)

    processed_message = session.get(Outbox, message_id)
    assert processed_message is not None
    assert processed_message.status == OutboxStatus.PENDING
    assert processed_message.error_message is not None
    assert "temporary network issue" in processed_message.error_message
    assert processed_message.processing_attempts == 1
    assert processed_message.available_at is not None
    assert processed_message.available_at > start_time.replace(tzinfo=None)
    mock_publisher.publish.assert_not_called()


@pytest.mark.asyncio
async def test_process_message_max_attempts_failure(
    session: Session, worker: OutboxWorker, mock_publisher: MagicMock, mock_gemini_service: AsyncMock
):
    """
    Tests that a message is moved to FAILED after exceeding max_attempts.
    """
    message = create_outbox_message(session, processing_attempts=worker.max_attempts - 1)
    message_id = message.id
    error = ValueError("Persistent transient error.")
    mock_gemini_service.side_effect = error

    await worker.process_message(message, mock_publisher)

    processed_message = session.get(Outbox, message_id)
    assert processed_message is not None
    assert processed_message.status == OutboxStatus.FAILED
    assert processed_message.processing_attempts == worker.max_attempts
    mock_publisher.publish.assert_not_called()


@pytest.mark.asyncio
@patch("app.services.outbox_worker_service.datetime")
async def test_recover_stale_messages(mock_datetime_module: MagicMock, session: Session, worker: OutboxWorker):
    """
    Tests that a message stuck in PROCESSING state is recovered.
    """
    now = datetime.datetime.now(datetime.timezone.utc)
    mock_datetime_module.datetime.now.return_value = now
    mock_datetime_module.timedelta = datetime.timedelta

    stale_time = now - datetime.timedelta(seconds=worker.processing_timeout.total_seconds() + 60)
    stale_message = create_outbox_message(session, status=OutboxStatus.PROCESSING, updated_at=stale_time)
    fresh_message = create_outbox_message(session, status=OutboxStatus.PROCESSING, updated_at=now)

    await worker.recover_stale_messages()

    recovered_message = session.get(Outbox, stale_message.id)
    assert recovered_message is not None
    assert recovered_message.status == OutboxStatus.PENDING
    assert recovered_message.error_message is not None
    assert "Processing timed out" in recovered_message.error_message

    not_recovered_message = session.get(Outbox, fresh_message.id)
    assert not_recovered_message is not None
    assert not_recovered_message.status == OutboxStatus.PROCESSING


@pytest.mark.asyncio
async def test_get_pending_messages_respects_batch_size(session: Session, worker: OutboxWorker):
    """
    Verifies that the worker only fetches a number of jobs equal to its batch_size.
    """
    for _ in range(worker.batch_size + 3):
        create_outbox_message(session)

    messages = await worker.get_pending_messages()

    assert len(messages) == worker.batch_size


@pytest.mark.asyncio
async def test_get_pending_messages_respects_available_at_and_order(session: Session, worker: OutboxWorker):
    """
    Ensures the worker ignores messages scheduled for a future retry and fetches the oldest first.
    """
    now = datetime.datetime.now(datetime.timezone.utc)

    oldest_message = create_outbox_message(session, created_at=now - datetime.timedelta(minutes=10))

    create_outbox_message(session, created_at=now - datetime.timedelta(minutes=5))

    create_outbox_message(session, available_at=now + datetime.timedelta(minutes=30))

    messages = await worker.get_pending_messages()

    assert len(messages) == 2
    assert messages[0].id == oldest_message.id


@pytest.mark.asyncio
async def test_process_message_handles_publish_failure(
    session: Session, worker: OutboxWorker, mock_publisher: MagicMock, mock_gemini_service: AsyncMock
):
    """
    Confirms a failure during Pub/Sub publishing results in a retry, ensuring the process is transactional.
    """
    message = create_outbox_message(session)
    message_id = message.id

    mock_gemini_service.return_value = (RiskItemDto(risk_items=[]), 10, 20, 0.001)

    publish_error = Exception("Pub/Sub unavailable")
    mock_publisher.publish.side_effect = publish_error

    await worker.process_message(message, mock_publisher)

    processed_message = session.get(Outbox, message_id)
    assert processed_message is not None
    assert processed_message.status == OutboxStatus.PENDING
    assert processed_message.processing_attempts == 1
    assert processed_message.error_message is not None
    assert str(publish_error) in processed_message.error_message
    mock_gemini_service.assert_awaited_once()


@pytest.mark.asyncio
async def test_run_main_loop_waits_when_no_messages(worker: OutboxWorker):
    """
    Verifies the main loop waits for `poll_interval` when the outbox is empty to avoid busy-waiting.
    """
    worker.is_running = True
    worker.get_pending_messages = AsyncMock(return_value=[])
    worker.recover_stale_messages = AsyncMock()

    with patch("asyncio.sleep", new_callable=AsyncMock) as mock_sleep:

        async def stop_loop_after_sleep(*args, **kwargs):
            worker.stop()

        mock_sleep.side_effect = stop_loop_after_sleep

        await worker.run_main_loop()

    worker.recover_stale_messages.assert_awaited_once()
    worker.get_pending_messages.assert_awaited_once()
    mock_sleep.assert_awaited_with(worker.poll_interval)


@pytest.mark.asyncio
async def test_worker_start_and_stop_lifecycle(worker: OutboxWorker):
    """
    Verifies that start() creates a task and stop() cancels it gracefully.
    """
    assert not worker.is_running
    assert worker.worker_task is None

    long_running_event = asyncio.Event()
    worker.run_main_loop = AsyncMock(side_effect=long_running_event.wait)

    worker.start()

    assert worker.is_running
    assert worker.worker_task is not None

    await asyncio.sleep(0)
    worker.run_main_loop.assert_awaited_once()

    running_task = worker.worker_task
    assert not running_task.done()

    worker.stop()

    assert not worker.is_running
    assert worker.worker_task is None

    await asyncio.sleep(0)

    assert running_task.cancelled()
