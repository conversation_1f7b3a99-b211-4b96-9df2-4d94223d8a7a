import pytest

from app.enums.llm_enum import LlmModel
from app.llm.pricing import calculate_ai_cost


@pytest.mark.parametrize(
    "model, input_tokens, output_tokens, expected_cost",
    [
        # Gemini Pro 1.5 - Tier 1 (<= 128k total tokens)
        (LlmModel.GEMINI_PRO_15, 100_000, 28_000, (100_000 / 1_000_000) * 1.25 + (28_000 / 1_000_000) * 5.0),
        # Gemini Pro 1.5 - Tier 1 Boundary (exactly 128k total tokens)
        (LlmModel.GEMINI_PRO_15, 128_000, 0, (128_000 / 1_000_000) * 1.25 + (0 / 1_000_000) * 5.0),
        # Gemini Pro 1.5 - Tier 2 (> 128k total tokens)
        (LlmModel.GEMINI_PRO_15, 128_001, 50_000, (128_001 / 1_000_000) * 2.50 + (50_000 / 1_000_000) * 10.0),
        (LlmModel.GEMINI_PRO_15, 200_000, 50_000, (200_000 / 1_000_000) * 2.50 + (50_000 / 1_000_000) * 10.0),
        # Gemini Pro 2.5 - Tier 1 (<= 200k total tokens)
        (LlmModel.GEMINI_PRO_25, 150_000, 50_000, (150_000 / 1_000_000) * 1.25 + (50_000 / 1_000_000) * 10.0),
        # Gemini Pro 2.5 - Tier 1 Boundary (exactly 200k total tokens)
        (LlmModel.GEMINI_PRO_25, 200_000, 0, (200_000 / 1_000_000) * 1.25 + (0 / 1_000_000) * 10.0),
        # Gemini Pro 2.5 - Tier 2 (> 200k total tokens)
        (LlmModel.GEMINI_PRO_25, 200_001, 50_000, (200_001 / 1_000_000) * 2.50 + (50_000 / 1_000_000) * 15.0),
        (LlmModel.GEMINI_PRO_25, 300_000, 50_000, (300_000 / 1_000_000) * 2.50 + (50_000 / 1_000_000) * 15.0),
        # Gemini Flash 2.0 (Single Tier)
        (LlmModel.GEMINI_FLASH_20, 500_000, 100_000, (500_000 / 1_000_000) * 0.15 + (100_000 / 1_000_000) * 0.60),
        # Gemini Flash 2.5 (Single Tier)
        (LlmModel.GEMINI_FLASH_25, 500_000, 100_000, (500_000 / 1_000_000) * 0.15 + (100_000 / 1_000_000) * 3.5),
        # Gemini Flash 2.5 (High token count)
        (LlmModel.GEMINI_FLASH_25, 1_000_000, 100_000, (1_000_000 / 1_000_000) * 0.15 + (100_000 / 1_000_000) * 3.5),
    ],
)
def test_calculate_ai_cost(model: LlmModel, input_tokens: int, output_tokens: int, expected_cost: float):
    """Tests the AI cost calculation for various models and token counts."""
    actual_cost = calculate_ai_cost(model, input_tokens, output_tokens)
    assert actual_cost == pytest.approx(expected_cost)


def test_unsupported_model_handling():
    """Tests that an unsupported model name raises a ValueError."""
    with pytest.raises(ValueError, match="Unsupported model: UNSUPPORTED_MODEL"):
        calculate_ai_cost("UNSUPPORTED_MODEL", 100, 100)


def test_zero_tokens_cost():
    """Tests that the cost is zero when both input and output tokens are zero."""
    cost = calculate_ai_cost(LlmModel.GEMINI_PRO_15, 0, 0)
    assert cost == 0.0
