import json
from unittest.mock import MagicMock, patch
from uuid import uuid4

from app.enums.messaging_enum import MessageType
from app.services.google_messaging_service import (
    PROMPT_SUB,
    PROMPT_TOPIC,
    MessageDto,
    PubSubPublisher,
    PubSubSubscriber,
)


def test_publish_message():
    """
    Tests that the publisher calls the GCP client with the correct topic and message.
    """
    mock_publisher_client = MagicMock()
    with patch("app.services.google_messaging_service.PublisherClient", return_value=mock_publisher_client):
        publisher = PubSubPublisher(PROMPT_TOPIC)
        message = MessageDto(message_type=MessageType.TEST, data={"foo": "bar"}, id=uuid4().hex)
        publisher.publish(message)

        mock_publisher_client.publish.assert_called_once()
        call_args, call_kwargs = mock_publisher_client.publish.call_args
        assert call_args[0] == publisher._topic
        assert message.id in str(call_args[1])
        assert call_kwargs["correlation_id"] == message.id


def test_pull_message():
    """
    Tests that the subscriber correctly pulls and deserializes a message.
    """
    mock_subscriber_client = MagicMock()
    message_id = uuid4().hex
    dto = MessageDto(message_type=MessageType.TEST, data={"foo": "bar"}, id=message_id)

    mock_pubsub_message = MagicMock()
    mock_pubsub_message.data = json.dumps(
        {"message_type": dto.message_type.value, "data": dto.data, "id": dto.id}
    ).encode("utf-8")
    mock_pubsub_message.attributes = {"correlation_id": message_id}

    mock_response = MagicMock()
    mock_response.received_messages = [MagicMock(message=mock_pubsub_message)]
    mock_subscriber_client.pull.return_value = mock_response

    with patch("app.services.google_messaging_service.SubscriberClient", return_value=mock_subscriber_client):
        subscriber = PubSubSubscriber(PROMPT_SUB)
        pulled_message = subscriber.pull(message_id, total_timeout=5, pull_timeout=1)

        mock_subscriber_client.pull.assert_called()
        assert pulled_message is not None
        assert pulled_message.id == message_id
        assert pulled_message.data == {"foo": "bar"}
