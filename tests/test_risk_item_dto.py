import pytest
from pydantic import ValidationError

from app.dtos.risk_item_dto import RiskItemDto, SingleRiskItemDto


@pytest.mark.parametrize(
    "input_data, expected_rule_id, expected_text, expected_explanation",
    [
        (
            {"rule_id": 1, "violation_text": "Violation", "violation_explanation": "Explanation"},
            1,
            "Violation",
            "Explanation",
        ),
        (
            {"violation_text": "Violation", "violation_explanation": "Explanation"},
            None,
            "Violation",
            "Explanation",
        ),
        ({"rule_id": 2, "violation_explanation": "Explanation"}, 2, None, "Explanation"),
        (
            {"rule_id": "5", "violation_text": "Violation", "violation_explanation": "Explanation"},
            5,
            "Violation",
            "Explanation",
        ),
        (
            {"rule_id": None, "violation_text": None, "violation_explanation": None},
            None,
            None,
            None,
        ),
        (
            {"rule_id": 12, "violation_text": "Violation", "extra_field": "Should be removed"},
            12,
            "Violation",
            "",
        ),
        (
            {"rule_id": "abc", "violation_text": "Violation"},
            None,
            "Violation",
            "",
        ),
    ],
    ids=[
        "valid_full_data",
        "missing_rule_id",
        "missing_violation_text",
        "string_rule_id_is_converted",
        "all_fields_are_none",
        "extra_fields_are_ignored",
        "unconvertible_rule_id_becomes_none",
    ],
)
def test_single_risk_item_dto_creation(input_data, expected_rule_id, expected_text, expected_explanation):
    """Tests various successful creation scenarios for SingleRiskItemDto."""
    item = SingleRiskItemDto(**input_data)
    assert item.rule_id == expected_rule_id
    assert item.violation_text == expected_text
    assert item.violation_explanation == expected_explanation
    assert "extra_field" not in item.model_dump()


@pytest.mark.parametrize(
    "input_data",
    [
        ({"rule_id": 1, "violation_text": 123, "violation_explanation": "Explanation"}),
        ({"rule_id": 1, "violation_text": "Violation", "violation_explanation": []}),
    ],
    ids=["invalid_type_for_violation_text", "invalid_type_for_explanation"],
)
def test_single_risk_item_dto_validation_error(input_data):
    """Tests that SingleRiskItemDto raises ValidationError for invalid data types."""
    with pytest.raises(ValidationError):
        SingleRiskItemDto(**input_data)


def test_risk_item_dto_handles_valid_data_and_defaults():
    """Tests valid RiskItemDto creation and that missing explanations are defaulted."""
    data = {
        "risk_items": [
            {
                "rule_id": 1,
                "violation_text": "Violation 1",
                "violation_explanation": "Explanation 1",
            },
            {"rule_id": 2, "violation_text": "Violation 2"},
        ]
    }
    dto = RiskItemDto(**data)  # type: ignore
    assert len(dto.risk_items) == 2
    assert dto.risk_items[0].rule_id == 1
    assert dto.risk_items[0].violation_explanation == "Explanation 1"
    assert dto.risk_items[1].rule_id == 2
    assert dto.risk_items[1].violation_explanation == ""


def test_risk_item_dto_with_empty_list():
    """Tests RiskItemDto with an empty risk_items list."""
    dto = RiskItemDto(risk_items=[])
    assert len(dto.risk_items) == 0


def test_risk_item_dto_filters_items_without_required_keys():
    """Tests that the RiskItemDto validator removes dicts without rule_id and violation_text."""
    data = {
        "risk_items": [
            {"rule_id": 1, "violation_text": "This one is valid"},
            {"rule_id": 2},
            {"violation_text": "Missing rule_id"},
            {"other_field": "Completely invalid"},
            {"rule_id": 3, "violation_text": "This one is also valid"},
        ]
    }
    dto = RiskItemDto(**data)  # type: ignore
    assert len(dto.risk_items) == 2
    assert dto.risk_items[0].rule_id == 1
    assert dto.risk_items[1].rule_id == 3
