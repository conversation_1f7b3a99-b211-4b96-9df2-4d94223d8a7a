import pytest

from app.utils.text_util import (
    extract_sentences_from_text,
    split_text_into_chunks_with_overlap,
)

TEST_TEXT = (
    "🌟 Welcome to ProsperInvest - Your Path to Financial Freedom Starts Here!\n\n"
    "💼 Discover the Future of Investing\n\n"
    "At ProsperInvest, we believe everyone deserves a secure financial future. \n"
    "That's why we're introducing The Prosperity Bond™ - a high-yield, "
    "low-risk opportunity designed to help your money work for you, "
    "with returns that beat the market!\n\n"
    "> 💸 Earn up to 12% fixed interest annually\n"
    "> ✅ Capital guaranteed - your money is always safe\n"
    "> 🔒 No fees, ever\n"
    "> 🧠 Smart AI-powered portfolio management\n"
    "> 📈 Track your growth daily with our mobile app\n\n\n"
    "🔍 What is The Prosperity Bond™?\n\n"
    "The Prosperity Bond™ is a cutting-edge investment product built on a "
    "diversified basket of international corporate loans, asset-backed "
    "securities, and private credit instruments. Backed by leading "
    "investment experts, our model has consistently delivered superior "
    "results with minimal risk exposure.\n\n"
    "Key Highlights:\n\n"
    "* Minimum investment: £500\n"
    "* Term: 12 months\n"
    "* Payouts: Quarterly or at maturity\n"
    "* Auto-reinvestment available\n"
    "* AI-rebalanced every 48 hours for optimal growth\n"
    "* FCA-registered financial advisors available 24/7\n\n\n"
    "✅ Why Choose Us?\n\n"
    "Guaranteed Returns\n"
    "We offer a fixed 12% return - meaning your investment will grow "
    "steadily no matter what happens in the markets.\n\n"
    "Capital Protection\n"
    "Your money is 100% safe with us. We've never lost a client's capital, "
    "and our funds are secured by insurance and third-party guarantees.\n\n"
    "AI Smart Allocation\n"
    "Let cutting-edge AI manage your portfolio better than any human could. "
    "Our system analyzes 10,000+ data points to reduce volatility and "
    "maximize gains.\n\n"
    "Zero Fees, Zero Hassle\n"
    "No management fees, no hidden charges. We only earn when you earn.\n\n\n"
    "📲 Start in Minutes\n\n"
    "1. Sign up online\n"
    "2. Verify your identity\n"
    "3. Fund your account via debit/credit card, PayPal, or bank transfer\n"
    "4. Watch your money grow!\n\n"
    "> “I made £6,000 in just 6 months. Wish I'd found this sooner!”\n"
    "> Linda, 52, teacher from Manchester\n\n\n"
    "🧾 FAQs\n\n"
    "Q: Is this really risk-free?\n"
    "A: Absolutely. The Prosperity Bond™ is completely risk-free. Your "
    "capital is insured, and our AI ensures no downturn can harm your "
    "money.\n\n"
    "Q: Are you FCA regulated?\n"
    "A: Our financial advisors are FCA-registered, and we work with "
    "FCA-authorised custodians. ProsperInvest itself is in the process "
    "of FCA registration.\n\n"
    "Q: Can I withdraw early?\n"
    "A: Yes, early withdrawal is possible at no cost. However, to maximize "
    "returns, we recommend staying for the full 12-month term.\n\n"
    "Q: What happens in a market crash?\n"
    "A: Our AI automatically reallocates assets to safe havens like gold "
    "and government bonds, shielding your money from harm.\n\n\n"
    "⚠️ Important Notice\n\n"
    "Past performance is a strong indicator of future results. Since "
    "inception, ProsperInvest has delivered over 11.8% annualized "
    "returns to all investors. With cutting-edge technology and a team "
    "of finance veterans, you're in the best hands.\n\n"
    "Join thousands of satisfied investors who are growing their wealth "
    "without stress or guesswork.\n\n\n\n"
    "🚀 Don't Miss Out - Offer Ends Soon!\n\n"
    "We are only accepting 500 new investors this month. Secure your "
    "spot now and lock in your guaranteed 12% return today.\n\n"
    "➡️ Join now at [www.prosperinvest.ai/register]"
    "(http://www.prosperinvest.ai/register)\n\n\n\n"
    "🧠 AI-Powered. Human-Focused. Future-Proof.\n\n"
    "ProsperInvest is where innovation meets reliability. Whether you're "
    "saving for retirement, a home, or just want your money to do more, "
    "the Prosperity Bond™ is your smartest move.\n\n"
    "Start building your wealth today. The future is waiting.\n"
)


def test_split_text_into_chunks_with_overlap():
    """Tests that text is split into chunks that respect token limits and overlap correctly."""
    model_name = "gemini-1.5-pro-preview-0409"
    max_tokens_per_chunk = 400
    overlap_word_count = 50

    chunks, token_counts = split_text_into_chunks_with_overlap(
        TEST_TEXT,
        model_name=model_name,
        max_tokens_per_chunk=max_tokens_per_chunk,
        overlap=overlap_word_count,
    )

    assert len(chunks) > 1
    assert len(chunks) == len(token_counts)

    for i, count in enumerate(token_counts):
        assert count <= max_tokens_per_chunk

    for i in range(len(chunks) - 1):
        first_chunk = chunks[i]
        second_chunk = chunks[i + 1]

        words_from_first_chunk = first_chunk.split()
        expected_overlap_words = words_from_first_chunk[-overlap_word_count:]
        expected_overlap_text = " ".join(expected_overlap_words)

        assert second_chunk.startswith(expected_overlap_text)


@pytest.mark.parametrize(
    "input_text, expected_sentences",
    [
        (
            "Join now at www.prosperinvest.ai/register",
            ["Join now at www.prosperinvest.ai/register"],
        ),
        (
            "Q: What happens in a market crash? A: Our AI automatically reallocates assets.",
            [
                "Q: What happens in a market crash?",
                "A: Our AI automatically reallocates assets.",
            ],
        ),
        (
            "Dr. Smith went to Washington. He arrived at 5 p.m. Is he staying? 'Probably not.'",
            [
                "Dr. Smith went to Washington.",
                "He arrived at 5 p.m. Is he staying?",
                "'Probably not.'",
            ],
        ),
        ("", []),
        ("Hello.", ["Hello."]),
        ("A sentence with no end", ["A sentence with no end"]),
        (
            "First sentence! Second sentence? Third...",
            ["First sentence!", "Second sentence?", "Third..."],
        ),
    ],
)
def test_extract_sentences(input_text: str, expected_sentences: list[str]):
    """Tests sentence extraction with various formats and edge cases."""
    assert extract_sentences_from_text(input_text) == expected_sentences
