#!/bin/bash

set -e  # Exit immediately if a command exits with a non-zero status.

commits=''
tags=$(git tag)

current_version=$(python -c "from app.version import __version__; print(__version__)")
IFS='.' read -r major minor patch <<< "$current_version"

# Fetch commits since last tag
if [[ ! -n "$tags" ]]; then
  echo "new_version=${current_version}" >> $GITHUB_ENV
  exit 0
else
  commits=$(git log $(git describe --tags --abbrev=0)..HEAD --pretty=format:%s)
  if [[ ! -n "$commits" ]]; then
    echo "skip_versioning=true" >> $GITHUB_ENV
    exit 0
  fi
fi

bump_type="patch"
for line in $commits; do
  if [[ "$line" =~ ^BREAKING[[:space:]]CHANGE ]]; then
    bump_type="major"
    break
  elif [[ "$line" =~ ^feat ]]; then
    bump_type="minor"
  fi
done

# Bump version based on bump_type
case $bump_type in
  major)
    major=$((major + 1))
    minor=0
    patch=0
    ;;
  minor)
    minor=$((minor + 1))
    patch=0
    ;;
  patch)
    patch=$((patch + 1))
    ;;
esac

new_version="$major.$minor.$patch"
echo "New version: $new_version"

# Set the outputs for GitHub Actions
echo "new_version="v${new_version}"" >> $GITHUB_ENV